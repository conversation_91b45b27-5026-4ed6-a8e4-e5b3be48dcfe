# Travel Pilot API - OpenAPI 规范文档

## ✅ 完成！已生成标准 OpenAPI 3.0 格式文档

我已经成功为您的 52 个 API 接口生成了标准的 OpenAPI 3.0 规范文档。

---

## 📄 生成的文件

### 🎯 **主要文件**
- **`travel-pilot-openapi.yaml`** - OpenAPI 3.0 YAML 格式规范
- **`travel-pilot-openapi.json`** - OpenAPI 3.0 JSON 格式规范

### 📊 **规范统计**
- **📡 接口数量**: 52 个 API endpoints
- **🏷️ 分类标签**: 7 个业务分类
- **📋 数据模型**: 12 个可复用 Schema
- **🌍 服务环境**: 3 个部署环境（生产/测试/开发）

---

## 🏗️ **OpenAPI 规范结构**

### 📋 **基本信息**
```yaml
openapi: 3.0.3
info:
  title: Travel Pilot API
  description: 企业差旅管理系统接口规范
  version: 1.0.0
  contact:
    name: Travel Pilot API Support
    email: <EMAIL>
```

### 🌐 **服务器环境**
- **生产环境**: `https://api.travelpilot.com/v1`
- **测试环境**: `https://staging-api.travelpilot.com/v1`
- **开发环境**: `https://dev-api.travelpilot.com/v1`

### 🏷️ **API 分类标签**
1. **authentication** - 身份认证相关接口
2. **employee** - 员工管理相关接口
3. **traveler** - 出行人管理相关接口
4. **booking** - 预订管理相关接口
5. **policy** - 差旅政策相关接口
6. **travel-order** - 差旅单管理相关接口
7. **enterprise** - 企业管理相关接口

### 🔐 **安全认证**
```yaml
security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
```

### 📋 **可复用数据模型 (Schemas)**
- **SuccessResponse** - 统一成功响应格式
- **ErrorResponse** - 统一错误响应格式
- **Pagination** - 分页信息结构
- **Organization** - 组织架构信息
- **Contact** - 联系方式信息
- **Document** - 证件信息结构
- **Card** - 支付卡片信息
- **POI** - 地理位置信息
- **Policy** - 差旅政策结构
- **Coordinates** - 坐标信息
- **EmergencyContact** - 紧急联系人
- **PolicyRules** - 政策规则

---

## 🚀 **使用方式**

### 1️⃣ **在线查看 (推荐)**
使用 Swagger UI 在线查看和测试：

```bash
# 使用 Docker 快速启动 Swagger UI
docker run -p 8080:8080 -e SWAGGER_JSON=/app/openapi.yaml -v $(pwd):/app swaggerapi/swagger-ui

# 然后访问: http://localhost:8080
```

### 2️⃣ **代码生成**
使用 OpenAPI Generator 生成客户端 SDK：

```bash
# 安装 OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成 JavaScript SDK
openapi-generator-cli generate -i travel-pilot-openapi.yaml -g javascript -o ./sdk/javascript

# 生成 Python SDK  
openapi-generator-cli generate -i travel-pilot-openapi.yaml -g python -o ./sdk/python

# 生成 Java SDK
openapi-generator-cli generate -i travel-pilot-openapi.yaml -g java -o ./sdk/java
```

### 3️⃣ **API 测试工具**
- **Postman**: 导入 `travel-pilot-openapi.json` 文件
- **Insomnia**: 导入 OpenAPI 规范文件
- **curl**: 根据规范生成 curl 命令

### 4️⃣ **文档生成**
使用 Redoc 生成美观的 API 文档：

```bash
# 安装 redoc-cli
npm install -g redoc-cli

# 生成静态 HTML 文档
redoc-cli build travel-pilot-openapi.yaml --output api-docs.html
```

---

## 📝 **接口示例**

### 🔸 **员工信息同步接口**
```yaml
/api/v1/employee/sync:
  post:
    tags:
      - employee
    summary: 员工信息同步（预订人）
    description: 员工信息同步（预订人）接口，用于同步和获取员工基本信息
    parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              employee_id:
                type: string
                pattern: ^EMP[0-9]{3,}$
                description: 员工唯一标识符
                example: EMP001
              sso_token:
                type: string
                description: 单点登录认证令牌
                minLength: 100
    responses:
      '200':
        description: 成功响应
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_id:
                          type: string
                          description: 员工唯一标识符
                        enterprise_id:
                          type: string
                          description: 企业唯一标识符
                        employee_name:
                          type: string
                          description: 员工姓名
                        is_express_booking:
                          type: boolean
                          description: 是否为极速预订员工
```

---

## 🛠️ **开发工具集成**

### **VS Code 扩展**
- **OpenAPI (Swagger) Editor** - 编辑和预览 OpenAPI 文档
- **REST Client** - 直接在 VS Code 中测试 API

### **IntelliJ IDEA 插件**
- **OpenAPI Specifications** - OpenAPI 规范支持
- **HTTP Client** - API 测试工具

### **在线工具**
- **Swagger Editor**: https://editor.swagger.io/
- **Swagger UI**: https://petstore.swagger.io/
- **Redoc**: https://redocly.github.io/redoc/

---

## 🔍 **验证和校验**

### **语法验证**
```bash
# 使用 swagger-codegen 验证
swagger-codegen validate -i travel-pilot-openapi.yaml

# 使用 openapi-generator 验证
openapi-generator-cli validate -i travel-pilot-openapi.yaml
```

### **在线验证**
- 访问 [Swagger Editor](https://editor.swagger.io/) 
- 导入 `travel-pilot-openapi.yaml` 文件
- 自动进行语法检查和格式验证

---

## 📈 **版本管理**

OpenAPI 规范支持版本控制，当 API 发生变化时：

1. **更新版本号** - 修改 `info.version`
2. **添加变更说明** - 在 `info.description` 中说明变更
3. **向后兼容** - 使用 `deprecated: true` 标记废弃接口
4. **新版本发布** - 创建新的规范文件版本

---

## ✨ **优势特性**

✅ **标准化** - 遵循 OpenAPI 3.0.3 标准规范  
✅ **工具兼容** - 支持主流 API 工具和平台  
✅ **代码生成** - 可自动生成多语言 SDK  
✅ **文档生成** - 可生成交互式 API 文档  
✅ **测试支持** - 支持 API 测试和 Mock  
✅ **版本控制** - 支持 API 版本管理  
✅ **团队协作** - 统一的 API 设计和开发规范  

现在您拥有了完整的 OpenAPI 3.0 格式的接口规范文档，可以与各种开发工具和平台无缝集成！