# Travel Pilot API 接口规范文档

## 📋 概览

本文档包含了 Travel Pilot 企业差旅管理系统的完整接口规范，共 52 个 API 接口，每个接口都提供了详细的 JSON 格式说明、请求示例和响应示例。

## 🎯 接口分类

### 按业务流程分类：
- **核心主流程**: 26 个接口
- **出行人选择/确认子流程**: 3 个接口  
- **辅助要素补全子流程**: 6 个接口
- **差旅管控子流程**: 3 个接口
- **创建差旅单子流程**: 4 个接口
- **出行偏好信息子流程**: 3 个接口
- **创单验证子流程**: 2 个接口
- **对话采集出行要素子流程、辅助要素补全子流程**: 1 个接口
- **出行喜好子流程**: 1 个接口
- **其他**: 3 个接口

### 按优先级分类：
- **🔥 高优先级**: 15 个接口
- **🟨 中优先级**: 15 个接口  
- **🟩 低优先级**: 8 个接口

## 🚀 核心接口示例

### 1. 员工信息同步接口 (API-1)

**接口地址**: `POST /api/v1/employee/sync`

**请求示例**:
```json
{
  "employee_id": "EMP001",
  "sso_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**成功响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "employee_id": "EMP001",
    "enterprise_id": "ENT001",
    "employee_name": "张三",
    "is_express_booking": true
  },
  "timestamp": "2025-07-31 14:30:25"
}
```

### 2. 获取出行人详细信息接口 (API-3)

**接口地址**: `POST /api/v1/traveler/details`

**请求示例**:
```json
{
  "traveler_name": "张三",
  "employee_id": "EMP001"
}
```

**成功响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "employee_id": "EMP001",
    "employee_name": "张三",
    "vip_level": "VIP1",
    "organization": {
      "department": "技术部",
      "team": "后端开发组", 
      "level": "高级工程师"
    },
    "contact": {
      "phone": "13800138000",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-07-31 14:30:25"
}
```

### 3. 创建差旅单接口 (API-17)

**接口地址**: `POST /api/v1/travel-order/create`

**请求示例**:
```json
{
  "employee_id": "EMP001",
  "departure": "北京",
  "destination": "上海",
  "departure_time": "2025-08-15 09:00:00",
  "return_time": "2025-08-17 18:00:00"
}
```

**成功响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "order_id": "TO2025073100001",
    "employee_id": "EMP001",
    "status": "created",
    "departure": "北京",
    "destination": "上海",
    "departure_time": "2025-08-15 09:00:00",
    "return_time": "2025-08-17 18:00:00"
  },
  "timestamp": "2025-07-31 14:30:25"
}
```

## 🔧 公共参数

### 请求头 (Headers)
- **Content-Type**: `application/json`
- **Authorization**: `Bearer {access_token}`
- **X-Enterprise-ID**: `{enterprise_id}`

### 公共响应格式
```json
{
  "code": 200,          // 状态码: 200-成功, 400-参数错误, 401-未授权, 500-服务器错误
  "message": "success", // 响应消息
  "data": {},          // 响应数据
  "timestamp": "2025-07-31 14:30:25" // 响应时间
}
```

## 🔐 身份认证

### 获取访问令牌
**接口地址**: `POST /api/v1/auth/token`

**请求示例**:
```json
{
  "client_id": "your_client_id",
  "client_secret": "your_client_secret", 
  "grant_type": "client_credentials"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  },
  "timestamp": "2025-07-31 14:30:25"
}
```

## 📱 使用方式

1. **在线查看**: 打开 `travel-pilot-api-docs-detailed.html` 文件
2. **搜索功能**: 使用侧边栏搜索框快速找到相关接口
3. **分类浏览**: 按业务流程分组查看接口
4. **详细示例**: 每个接口都提供完整的 JSON 请求/响应示例
5. **复制代码**: 点击示例代码右上角的"复制"按钮

## 🛠️ 技术特性

- ✅ 52 个完整 API 接口规范
- ✅ 详细的 JSON 请求/响应示例  
- ✅ 参数类型和说明
- ✅ 错误处理示例
- ✅ 响应式设计，支持移动端
- ✅ 实时搜索功能
- ✅ 代码复制功能
- ✅ 标签页分类展示

## 📄 文件清单

- `travel-pilot-api-docs-detailed.html` - 完整的 API 文档 (推荐)
- `travel-pilot-api-docs.html` - 基础版 API 文档
- `api_json_specs.json` - JSON 规范数据
- `api_data.json` - 原始 API 数据
- `README-API-SPECS.md` - 本说明文档

---

**生成时间**: 2025-07-31  
**接口版本**: v1.0  
**文档版本**: 详细版 1.0