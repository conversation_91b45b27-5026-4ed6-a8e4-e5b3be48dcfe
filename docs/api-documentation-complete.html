        
        // Group APIs by workflow
        function groupApisByWorkflow() {
            const workflows = {};
            apiData.forEach(api => {
                const workflow = api['所属流程'] || '其他';
                if (!workflows[workflow]) {
                    workflows[workflow] = [];
                }
                workflows[workflow].push(api);
            });
            return workflows;
        }
        
        // Generate navigation
        function generateNavigation() {
            const navigation = document.getElementById('apiNavigation');
            const workflows = groupApisByWorkflow();
            
            // Add common sections
            navigation.innerHTML = `
                <li><a href="#common-params">🔧 公共参数</a></li>
                <li><a href="#access-token">🔐 身份认证</a></li>
                <li><a href="#sso">👤 单点登录</a></li>
                <li style="border-top: 1px solid #eee; margin-top: 10px; padding-top: 10px;"></li>
            `;
            
            Object.keys(workflows).forEach(workflow => {
                const li = document.createElement('li');
                li.innerHTML = `<a href="#workflow-${workflow}">${workflow} (${workflows[workflow].length})</a>`;
                navigation.appendChild(li);
            });
        }
        
        // Generate API list
        function generateApiList() {
            const apiList = document.getElementById('apiList');
            const workflows = groupApisByWorkflow();
            
            Object.keys(workflows).forEach(workflow => {
                const section = document.createElement('div');
                section.className = 'api-section';
                section.id = `workflow-${workflow}`;
                
                section.innerHTML = `
                    <h2>📋 ${workflow}</h2>
                    ${workflows[workflow].map(api => generateApiItem(api)).join('')}
                `;
                
                apiList.appendChild(section);
            });
        }
        
        // Generate individual API item
        function generateApiItem(api) {
            const priorityClass = api['优先级'] === '高' ? 'priority-high' : 
                                api['优先级'] === '中' ? 'priority-medium' : 'priority-low';
            
            const apiId = `api-${api['接口编号']}`;
            
            return `
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('${apiId}')">
                        <div>
                            <span class="api-id">API-${api['接口编号']}</span>
                            <span class="api-title">${api['接口名称']}</span>
                        </div>
                        <div>
                            <span class="api-priority ${priorityClass}">${api['优先级']}优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="${apiId}">
                        <div class="param-section">
                            <h4>📝 接口描述</h4>
                            <div class="param-content">
                                <p>${api['接口名称']} - 用于${api['所属流程']}。</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📤 请求参数</h4>
                            <div class="param-content">
                                ${formatParams(api['入参'])}
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📥 响应参数</h4>
                            <div class="param-content">
                                ${formatParams(api['必要出参'])}
                            </div>
                        </div>
                        
                        ${api['出参说明'] && api['出参说明'] !== '—' ? `
                        <div class="param-section">
                            <h4>📋 说明</h4>
                            <div class="param-content">
                                <p>${api['出参说明']}</p>
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="meta-info">
                            <div class="meta-item">
                                <span>🎯</span>
                                <span>所属流程: ${api['所属流程']}</span>
                            </div>
                            <div class="meta-item">
                                <span>📅</span>
                                <span>计划交付: ${api['计划交付时间']}</span>
                            </div>
                            <div class="meta-item">
                                <span>⚡</span>
                                <span>优先级: ${api['优先级']}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Format parameters
        function formatParams(params) {
            if (!params || params === '—') {
                return '<p>无特殊参数</p>';
            }
            
            // Split by common delimiters and format
            const items = params.split(/[、，,；;]/).filter(item => item.trim());
            if (items.length > 1) {
                return '<ul>' + items.map(item => `<li><strong>${item.trim()}</strong></li>`).join('') + '</ul>';
            } else {
                return `<p><strong>${params}</strong></p>`;
            }
        }
        
        // Toggle API content visibility
        function toggleApi(apiId) {
            const content = document.getElementById(apiId);
            const isActive = content.classList.contains('active');
            
            // Close all other API contents
            document.querySelectorAll('.api-content.active').forEach(el => {
                el.classList.remove('active');
                el.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▼';
            });
            
            // Toggle current API content
            if (!isActive) {
                content.classList.add('active');
                content.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▲';
            }
        }
        
        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const apiItems = document.querySelectorAll('.api-item');
                
                apiItems.forEach(item => {
                    const title = item.querySelector('.api-title').textContent.toLowerCase();
                    const content = item.textContent.toLowerCase();
                    
                    if (title.includes(query) || content.includes(query)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }
        
        // Initialize the documentation
        document.addEventListener('DOMContentLoaded', function() {
            generateNavigation();
            generateApiList();
            setupSearch();
            
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>