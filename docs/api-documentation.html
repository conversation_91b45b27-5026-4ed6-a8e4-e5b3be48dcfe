<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Pilot API 接口文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .sidebar {
            position: fixed;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 250px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .sidebar ul {
            list-style: none;
        }
        
        .sidebar li {
            margin-bottom: 8px;
        }
        
        .sidebar a {
            text-decoration: none;
            color: #666;
            font-size: 14px;
            display: block;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .sidebar a:hover {
            background-color: #f0f2ff;
            color: #667eea;
        }
        
        .content {
            margin-left: 290px;
        }
        
        .common-params {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .api-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .api-section h2 {
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f2ff;
        }
        
        .api-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .api-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .api-header:hover {
            background: #f0f2ff;
        }
        
        .api-title {
            font-weight: bold;
            color: #495057;
        }
        
        .api-id {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .api-priority {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .priority-high {
            background: #ff6b6b;
            color: white;
        }
        
        .priority-medium {
            background: #ffd93d;
            color: #333;
        }
        
        .priority-low {
            background: #6bcf7f;
            color: white;
        }
        
        .api-content {
            padding: 20px;
            display: none;
        }
        
        .api-content.active {
            display: block;
        }
        
        .param-section {
            margin-bottom: 20px;
        }
        
        .param-section h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .param-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .meta-info {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            font-size: 14px;
            color: #666;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            .content {
                margin-left: 0;
            }
        }
        
        .search-box {
            margin-bottom: 20px;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .toggle-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Travel Pilot API 接口文档</h1>
            <p>企业差旅管理系统接口规范</p>
        </div>
        
        <div class="sidebar">
            <h3>接口导航</h3>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索接口...">
            </div>
            <ul id="apiNavigation"></ul>
        </div>
        
        <div class="content">
            <!-- 公共参数 -->
            <div class="common-params" id="common-params">
                <h2>🔧 公共参数</h2>
                <div class="param-section">
                    <h4>请求头 (Headers)</h4>
                    <div class="param-content">
                        <p><strong>Content-Type:</strong> application/json</p>
                        <p><strong>Authorization:</strong> Bearer {access_token}</p>
                        <p><strong>X-Enterprise-ID:</strong> {企业ID}</p>
                    </div>
                </div>
                
                <div class="param-section">
                    <h4>公共响应参数</h4>
                    <div class="param-content">
                        <p><strong>code:</strong> 响应状态码 (200: 成功, 400: 参数错误, 401: 未授权, 500: 服务器错误)</p>
                        <p><strong>message:</strong> 响应消息</p>
                        <p><strong>data:</strong> 响应数据</p>
                        <p><strong>timestamp:</strong> 响应时间戳</p>
                    </div>
                </div>
            </div>
            
            <!-- 获取访问令牌 -->
            <div class="api-section" id="access-token">
                <h2>🔐 身份认证</h2>
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('auth-token')">
                        <div>
                            <span class="api-title">获取访问令牌 (Access Token)</span>
                        </div>
                        <div>
                            <span class="api-priority priority-high">高优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="auth-token">
                        <div class="param-section">
                            <h4>📝 接口描述</h4>
                            <div class="param-content">
                                <p>通过企业凭证获取API访问令牌，用于后续接口调用的身份验证。</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📤 请求参数</h4>
                            <div class="param-content">
                                <p><strong>client_id:</strong> 企业客户端ID</p>
                                <p><strong>client_secret:</strong> 企业客户端密钥</p>
                                <p><strong>grant_type:</strong> 授权类型 (固定值: client_credentials)</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📥 响应参数</h4>
                            <div class="param-content">
                                <p><strong>access_token:</strong> 访问令牌</p>
                                <p><strong>token_type:</strong> 令牌类型 (Bearer)</p>
                                <p><strong>expires_in:</strong> 令牌有效期 (秒)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 单点登录 -->
            <div class="api-section" id="sso">
                <h2>👤 单点登录</h2>
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('sso-login')">
                        <div>
                            <span class="api-title">员工单点登录认证</span>
                        </div>
                        <div>
                            <span class="api-priority priority-high">高优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="sso-login">
                        <div class="param-section">
                            <h4>📝 接口描述</h4>
                            <div class="param-content">
                                <p>通过企业单点登录系统验证员工身份，获取员工基本信息。</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📤 请求参数</h4>
                            <div class="param-content">
                                <p><strong>sso_token:</strong> 单点登录令牌</p>
                                <p><strong>employee_id:</strong> 员工ID (可选)</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📥 响应参数</h4>
                            <div class="param-content">
                                <p><strong>employee_id:</strong> 员工ID</p>
                                <p><strong>enterprise_id:</strong> 企业ID</p>
                                <p><strong>employee_name:</strong> 员工姓名</p>
                                <p><strong>is_express_booking:</strong> 是否极速预订员工</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- API接口列表 -->
            <div id="apiList"></div>
        </div>
    </div>

    <script>
        const apiData = [
  {
    "接口编号": 1,
    "接口名称": "员工信息同步（预订人）",
    "入参": "员工ID/单点登录",
    "必要出参": "员工ID、企业ID、是否极速预订员工（员工+公司）",
    "出参说明": "—",
    "所属流程": "核心主流程",
    "优先级": "高",
    "计划交付时间": "2025-08-11"
  },
  {
    "接口编号": 2,
    "接口名称": "获取员工代订类别",
    "入参": "员工ID",
    "必要出参": "代订类别枚举（本人、同事-所有员工、同事-自定义范围、客人）",
    "出参说明": "枚举值返回，可多值返回，如（本人、同事-所有员工",
    "所属流程": "出行人选择/确认子流程",
    "优先级": "高",
    "计划交付时间": "2025-08-11"
  },
  {
    "接口编号": 3,
    "接口名称": "获取出行人详细信息",
    "入参": "出行人姓名、员工ID",
    "必要出参": "件信息列表（类型、号码、有效期）、联系方式（手机、邮箱）、组织架构（全层级）、外部员工ID、VIP等级（否/VIP1/VIP2/VIP3）、base地",
    "出参说明": "获取时返回员工姓名=入参姓名的列表数据",
    "所属流程": "出行人选择/确认子流程",
    "优先级": "高",
    "计划交付时间": "2025-08-11"
  },
  {
    "接口编号": 4,
    "接口名称": "是否在代订范围（判断出行人是否在预订人的代订范围）",
    "入参": "预订人-员工ID、出行人-员工ID（支持多值，如：张三，李四）",
    "必要出参": "员工ID、是否在可订范围（键值对返回）",
    "出参说明": "员工ID获取时精准返回，姓名获取时返回员工姓名=入参姓名的列表数据",
    "所属流程": "出行人选择/确认子流程",
    "优先级": "中",
    "计划交付时间": "2025-08-22"
  },
  {
    "接口编号": 5,
    "接口名称": "获取出行人卡信息",
    "入参": "员工ID",
    "必要出参": "卡类型、所属机构、卡号、是否优选使用",
    "出参说明": "—",
    "所属流程": "核心主流程",
    "优先级": "中",
    "计划交付时间": "2025-08-22"
  }
];