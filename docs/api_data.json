[{"接口编号": 1, "接口名称": "员工信息同步（预订人）", "入参": "员工ID/单点登录", "必要出参": "员工ID、企业ID、是否极速预订员工（员工+公司）", "出参说明": "—", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 2, "接口名称": "获取员工代订类别", "入参": "员工ID", "必要出参": "代订类别枚举（本人、同事-所有员工、同事-自定义范围、客人）", "出参说明": "枚举值返回，可多值返回，如（本人、同事-所有员工", "所属流程": "出行人选择/确认子流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 3, "接口名称": "获取出行人详细信息", "入参": "出行人姓名、员工ID", "必要出参": "件信息列表（类型、号码、有效期）、联系方式（手机、邮箱）、组织架构（全层级）、外部员工ID、VIP等级（否/VIP1/VIP2/VIP3）、base地", "出参说明": "获取时返回员工姓名=入参姓名的列表数据", "所属流程": "出行人选择/确认子流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 4, "接口名称": "是否在代订范围（判断出行人是否在预订人的代订范围）", "入参": "预订人-员工ID、出行人-员工ID（支持多值，如：张三，李四）", "必要出参": "员工ID、是否在可订范围（键值对返回）", "出参说明": "员工ID获取时精准返回，姓名获取时返回员工姓名=入参姓名的列表数据", "所属流程": "出行人选择/确认子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 5, "接口名称": "获取出行人卡信息", "入参": "员工ID", "必要出参": "卡类型、所属机构、卡号、是否优选使用", "出参说明": "—", "所属流程": "核心主流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 6, "接口名称": "获取员工喜好", "入参": "员工ID", "必要出参": "喜好类型、喜好内容", "出参说明": "—", "所属流程": "出行喜好子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 7, "接口名称": "获取员工是否高消费", "入参": "员工ID", "必要出参": "键值对（员工ID-是/否）", "出参说明": "—", "所属流程": "出行偏好信息子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 8, "接口名称": "获取员工各品类是否管控差标", "入参": "员工ID", "必要出参": "键值对（员工ID-是/否）", "出参说明": "—", "所属流程": "出行偏好信息子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 9, "接口名称": "获取企业POI", "入参": "企业ID", "必要出参": "公司地址、POI类型、所在城市、高德经纬度、百度经纬度", "出参说明": "—", "所属流程": "出行偏好信息子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 10, "接口名称": "获取企业参照人信息", "入参": "企业ID", "必要出参": "用户自主选择/固定位预订人", "出参说明": "获取指定员工的预订权限，根据公司预订权限、员工预订权限、员工代订权限计算所得的最终结果", "所属流程": "核心主流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 11, "接口名称": "获取出行人管控政策（预定管控）", "入参": "出行人员工ID", "必要出参": "预定管控政策明细，字段包含政策名称、需要差旅单预定、预订管控产品、使用差旅单差标、启用差旅单预订权限、启用差旅单预算管控、启用票数限制、启用房间数限制、启用用车次数限制、差旅单管控", "出参说明": "包含出行人且状态为“启用”的预定管控政策", "所属流程": "差旅管控子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 12, "接口名称": "获取出行人有效出差申请单", "入参": "出行人员工ID", "必要出参": "出差申请单内容，字段包含差旅单单号、差旅类型、申请时间、更新时间、操作类型、差旅内容、差旅开始时间、差旅结束时间、差旅地点（差旅出发地、差旅目的地、其他地点）、base地、差旅人信息、差旅信息、可预订产品、可预订数量、差旅政策代码（国内外机票、火车票、酒店、职级）、状态", "出参说明": "包含出行人且出差申请单结束时间>=当天的出差申请单", "所属流程": "差旅管控子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 13, "接口名称": "获取出行人管控政策简单（第一版暂不实施）", "入参": "员工ID", "必要出参": "申请管控/差旅单申请开关：开/关、外部对接开关：开/关", "出参说明": "—", "所属流程": "差旅管控子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 14, "接口名称": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）", "入参": "员工ID", "必要出参": "申请管控政策细则，字段包含政策名称、需要差旅单预定、可预订产品、出差事由是否必填、启用票数限制、启用房间数限制、启用用车次数限制", "出参说明": "", "所属流程": "创建差旅单子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 15, "接口名称": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）", "入参": "员工ID", "必要出参": "申请管控政策细则，字段包含政策名称、需要差旅单预定、可预订产品、出差事由是否必填、启用票数限制、启用房间数限制、启用用车次数限制", "出参说明": "", "所属流程": "创建差旅单子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 16, "接口名称": "创建出差申请单（内）（第一版暂不实施）", "入参": "差旅类型、出差事由、差旅范围、预订产品、出行人信息、差旅行程", "必要出参": "创建状态", "出参说明": "在外部创建=开的状态下，需要将出差申请单同步至外部", "所属流程": "创建差旅单子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 17, "接口名称": "创建出差申请单（外）（第一版暂不实施）", "入参": "出差申请单内容，字段包含差旅单单号、差旅类型、申请时间、更新时间、操作类型、差旅内容、差旅开始时间、差旅结束时间、差旅地点（差旅出发地、差旅目的地、其他地点）、base地、差旅人信息、差旅信息、可预订产品、可预订数量、差旅政策代码（国内外机票、火车票、酒店、职级）、状态", "必要出参": "创建状态", "出参说明": "在外部创建=开的状态下，需要将出差申请单同步至外部", "所属流程": "创建差旅单子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 18, "接口名称": "获取出行人当前位置", "入参": "出行人员工ID", "必要出参": "当前位置名称、经纬度", "出参说明": "—", "所属流程": "对话采集出行要素子流程、辅助要素补全子流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 19, "接口名称": "获取员工产品预订权限", "入参": "出行人员工ID", "必要出参": "可定品类", "出参说明": "—", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 20, "接口名称": "获取出行人未出行订单-航班（第一版暂不实施）", "入参": "出行人员工ID", "必要出参": "订单号、订单类别、差旅类型（因私、因公）、关联ID、订单状态、出差申请单号、外部OA单号、航班号、航司二字码、出发机场所在城市名称、出发时间、完整到达时间、到达机场所在城市名称、订单总价格", "出参说明": "只返回已出票、出票中、待提交三种状态的订单", "所属流程": "辅助要素补全子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 21, "接口名称": "获取出行人未出行订单-火车票（第一版暂不实施）", "入参": "出行人员工ID", "必要出参": "订单号、订单类别、差旅类型（因私、因公）、关联ID、订单状态、出差申请单号、外部OA单号、车次号、出发城市、发车时间、到达时间、到达城市、坐席类别、座位号、票价", "出参说明": "只返回已出票、出票中、待提交三种状态的订单", "所属流程": "辅助要素补全子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 22, "接口名称": "获取出行人未出行订单-酒店（第一版暂不实施）", "入参": "出行人员工ID", "必要出参": "订单号、差旅类型（因私、因公）、订单状态、出差申请单号、外部OA单号、酒店名称、入住时间、离店时间、取消类型、限时取消时间、酒店地址、城市名称、房型名称、订单金额", "出参说明": "只返回订单状态为待入住、确认中、待提交三种状态的订单", "所属流程": "辅助要素补全子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 23, "接口名称": "获取出行人未出行订单-用车（第一版暂不实施）", "入参": "出行人员工ID", "必要出参": "订单号、订单类别、差旅类型（因私、因公）、订单状态、出差申请单号、外部OA单号、用车制度名称、用车事由描述、用车服务类型、预约出发时间、出发城市名称、到达城市名称、所需车型名称、出发地名称、出发经纬度、到达地名称、到达经纬度、总价", "出参说明": "只返回订单状态为待出行的订单", "所属流程": "辅助要素补全子流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 24, "接口名称": "获取出行人未使用机票（OPEN票）", "入参": "出行人员工ID", "必要出参": "航司名称、出发城市名称、到达城市名称、有效时间截止日期、状态", "出参说明": "—", "所属流程": "辅助要素补全子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 25, "接口名称": "获取出行人出发地用车与目的地用车历史订单", "入参": "出行人员工ID", "必要出参": "订单号、订单类别、差旅类型（因私、因公）、订单状态、出差申请单号、外部OA单号、用车制度名称、用车事由描述、用车服务类型、预约出发时间、出发城市名称、到达城市名称、所需车型名称、出发地名称、出发经纬度、到达地名称、到达经纬度、总价", "出参说明": "只返回订单状态为已完成的订单", "所属流程": "辅助要素补全子流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 26, "接口名称": "获取出行人所属公司授信/预存剩余额度是否>0", "入参": "公司ID", "必要出参": "是/否", "出参说明": "—", "所属流程": "核心主流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 27, "接口名称": "获取行程方案中地点与时间跨度的天气", "入参": "地点、日期、时间", "必要出参": "天气详情", "出参说明": "如有预警信息需返回", "所属流程": "核心主流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 28, "接口名称": "获取出行人历史相同行程订单-火车票", "入参": "出行人员工ID、出发城市、到达城市", "必要出参": "订单详情", "出参说明": "最近10条订单", "所属流程": "核心主流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 29, "接口名称": "获取出行人历史相同行程订单-机票", "入参": "出行人员工ID、出发城市、到达城市", "必要出参": "订单详情", "出参说明": "最近10条订单", "所属流程": "核心主流程", "优先级": "中", "计划交付时间": "2025-08-22"}, {"接口编号": 30, "接口名称": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）", "入参": "出行人员工ID、城市名", "必要出参": "订单详情", "出参说明": "出发城市最近10条订单、目的地城市最近最近10条订单，如没有则返回出行人所属公司历史订单", "所属流程": "核心主流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 31, "接口名称": "获取航班搜索列表（需要包含舱位与可购保险信息）", "入参": "出行人员工ID、出发城市ID、到达城市ID、出发日期", "必要出参": "航班列表完整信息（包含舱位、保险）、是否超标、谁超标（多人场景）、超标原因、超标是否可订", "出参说明": "需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 32, "接口名称": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）", "入参": "出行人员工ID、出发城市ID、到达城市ID、出发日期", "必要出参": "车次搜索列表信息、是否超标、谁超标（多人场景）、超标原因、超标是否可订", "出参说明": "需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 33, "接口名称": "获取精选酒店列表", "入参": "员工ID、城市ID、地点名称、入住时间、离店时间", "必要出参": "酒店列表信息、酒店推荐逻辑", "出参说明": "需要增加返回酒店推荐逻辑，在list中需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益", "所属流程": "", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 34, "接口名称": "获取酒店搜索列表", "入参": "出行人员工ID、城市ID、地点名称、入住时间、离店时间", "必要出参": "酒店搜索列表信息、酒店排序模型计算公式+得分、是否超标、谁超标（多人场景）、超标原因、超标是否可订", "出参说明": "需要增加返回现有模型计算公式+结果信息，返回共20条信息，在20条信息中需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 35, "接口名称": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）", "入参": "酒店ID、入店时间、离店时间", "必要出参": "酒店基本信息、酒店设施、酒店图片、房型政策信息、是否超标、谁超标（多人场景）、超标原因、超标是否可订", "出参说明": "—", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 36, "接口名称": "获取用车列表（第一版暂不实施）", "入参": "出行人员工ID、用车制度名称、出发时间、出发地点、到达地点", "必要出参": "用车列表信息", "出参说明": "要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益", "所属流程": "核心主流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 37, "接口名称": "获取出行人出差申请单状态", "入参": "出行人员工ID", "必要出参": "出差申请单单号、状态、审批人、审批信息", "出参说明": "—", "所属流程": "创单验证子流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 38, "接口名称": "获取出行人行程提交项启用状态", "入参": "出行人员工ID", "必要出参": "是/否", "出参说明": "—", "所属流程": "创单验证子流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 39, "接口名称": "获取结算金额信息", "入参": "产品提交预订信息", "必要出参": "总金额、支付方式、授信金额、个付金额、金额明细", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 40, "接口名称": "提交订单（支持多品类多订单）", "入参": "机票：航程类型、预定信息、预订乘客列表、联系人信息；\n酒店：城市、酒店ID、房型ID、政策ID、入住人、入离店时间等；\n火车票：请求key、车次号、车次类型、出发站、到达站等预订信息", "必要出参": "订单号", "出参说明": "一个行程多个订单，需要支持多品类一起下单", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 41, "接口名称": "申请改签机票订单", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 42, "接口名称": "确认改签机票订单", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 43, "接口名称": "变更酒店订单", "入参": "订单号、房间号、改期入住时间、改期离店时间、原因", "必要出参": "差旅订单号、订单号", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 44, "接口名称": "验证是否可改火车票订单", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 45, "接口名称": "申请改签火车票订单", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 46, "接口名称": "确认改签火车票订单", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "低", "计划交付时间": "2025-09-01"}, {"接口编号": 47, "接口名称": "12306账号登录", "入参": "12306账号、密码、登录模式、验证码、订单号、登录类型", "必要出参": "登录状态、核验状态", "出参说明": "—", "所属流程": "", "优先级": "", "计划交付时间": ""}, {"接口编号": 48, "接口名称": "12306常旅客信息查询", "入参": "12306账号、密码、查询类型", "必要出参": "姓名、证件号码、身份核验状态、是否可购票", "出参说明": "—", "所属流程": "", "优先级": "", "计划交付时间": ""}, {"接口编号": 49, "接口名称": "提交用车订单（第一版暂不实施）", "入参": "预订信息", "必要出参": "状态信息、订单号", "出参说明": "—", "所属流程": "核心主流程", "优先级": "", "计划交付时间": ""}, {"接口编号": 50, "接口名称": "提交火车票出票申请", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 51, "接口名称": "提交机票出票申请", "入参": "订单号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}, {"接口编号": 52, "接口名称": "提交保险订单", "入参": "航班号/车次号、日期、保险编号", "必要出参": "状态信息", "出参说明": "—", "所属流程": "核心主流程", "优先级": "高", "计划交付时间": "2025-08-11"}]