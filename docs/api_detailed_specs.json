{"api_1": {"api_id": 1, "api_name": "员工信息同步（预订人）", "method": "POST", "endpoint": "/api/v1/employee/sync", "description": "员工信息同步（预订人）接口，用于同步和获取员工基本信息", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "sso_token": {"type": "string", "required": true, "description": "单点登录认证令牌", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "minLength": 100}}, "example": {"employee_id": "EMP001", "sso_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"employee_id": {"type": "string", "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$"}, "enterprise_id": {"type": "string", "description": "企业唯一标识符", "example": "ENT001", "pattern": "^ENT[0-9]{3,}$"}, "is_express_booking": {"type": "boolean", "description": "是否为极速预订员工", "example": true}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001", "enterprise_id": "ENT001", "is_express_booking": true}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_2": {"api_id": 2, "api_name": "获取员工代订类别", "method": "POST", "endpoint": "/api/v1/employee/booking-types", "description": "获取员工代订类别接口，用于获取员工可代订的人员类别范围", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"booking_types": {"type": "array", "description": "可代订人员类别列表", "items": {"type": "string", "enum": ["本人", "同事-所有员工", "同事-自定义范围", "客人"]}, "example": ["本人", "同事-所有员工"], "minItems": 1}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"booking_types": ["本人", "同事-所有员工"]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_3": {"api_id": 3, "api_name": "获取出行人详细信息", "method": "POST", "endpoint": "/api/v1/traveler/details", "description": "获取出行人详细信息接口，用于获取指定出行人的详细个人信息", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_name": {"type": "string", "required": true, "description": "出行人员姓名", "example": "张三", "minLength": 2, "maxLength": 50}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "traveler_name": "张三", "traveler_employee_ids": ["EMP001", "EMP002"], "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"employee_id": {"type": "string", "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$"}, "organization": {"type": "object", "description": "员工组织架构信息", "properties": {"company_id": {"type": "string", "description": "公司ID", "example": "COMP001"}, "company_name": {"type": "string", "description": "公司名称", "example": "科技有限公司"}, "department_id": {"type": "string", "description": "部门ID", "example": "DEPT001"}, "department_name": {"type": "string", "description": "部门名称", "example": "技术部"}, "team_id": {"type": "string", "description": "团队ID", "example": "TEAM001"}, "team_name": {"type": "string", "description": "团队名称", "example": "后端开发组"}, "job_title": {"type": "string", "description": "职位名称", "example": "高级工程师"}, "job_level": {"type": "string", "description": "职级", "example": "P7"}, "manager_id": {"type": "string", "description": "直属领导员工ID", "example": "EMP100"}, "cost_center": {"type": "string", "description": "成本中心", "example": "CC001"}}, "example": {"company_id": "COMP001", "company_name": "科技有限公司", "department_id": "DEPT001", "department_name": "技术部", "team_id": "TEAM001", "team_name": "后端开发组", "job_title": "高级工程师", "job_level": "P7", "manager_id": "EMP100", "cost_center": "CC001"}}, "vip_level": {"type": "string", "description": "VIP等级", "enum": ["NONE", "VIP1", "VIP2", "VIP3"], "example": "VIP1"}, "contact": {"type": "object", "description": "联系方式信息", "properties": {"mobile": {"type": "string", "description": "手机号码", "example": "13800138000", "pattern": "^1[3-9]\\d{9}$"}, "email": {"type": "string", "format": "email", "description": "邮箱地址", "example": "zhang<PERSON>@company.com"}, "work_phone": {"type": "string", "description": "工作电话", "example": "010-12345678"}, "emergency_contact": {"type": "object", "description": "紧急联系人", "properties": {"name": {"type": "string", "description": "紧急联系人姓名", "example": "李四"}, "phone": {"type": "string", "description": "紧急联系人电话", "example": "13900139000"}, "relationship": {"type": "string", "description": "关系", "example": "配偶"}}}}, "example": {"mobile": "13800138000", "email": "zhang<PERSON>@company.com", "work_phone": "010-12345678", "emergency_contact": {"name": "李四", "phone": "13900139000", "relationship": "配偶"}}}, "pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001", "organization": {"company_id": "COMP001", "company_name": "科技有限公司", "department_id": "DEPT001", "department_name": "技术部", "team_id": "TEAM001", "team_name": "后端开发组", "job_title": "高级工程师", "job_level": "P7", "manager_id": "EMP100", "cost_center": "CC001"}, "vip_level": "VIP1", "contact": {"mobile": "13800138000", "email": "zhang<PERSON>@company.com", "work_phone": "010-12345678", "emergency_contact": {"name": "李四", "phone": "13900139000", "relationship": "配偶"}}, "pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_4": {"api_id": 4, "api_name": "是否在代订范围（判断出行人是否在预订人的代订范围）", "method": "POST", "endpoint": "/api/v1/booking/permission-check", "description": "是否在代订范围（判断出行人是否在预订人的代订范围）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"employee_id": {"type": "string", "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$"}, "booking_permissions": {"type": "array", "description": "代订权限详情", "items": {"type": "object", "properties": {"target_employee_id": {"type": "string", "description": "目标员工ID", "example": "EMP002"}, "target_employee_name": {"type": "string", "description": "目标员工姓名", "example": "李四"}, "can_book": {"type": "boolean", "description": "是否可以代订", "example": true}, "permission_reason": {"type": "string", "description": "权限原因", "example": "同部门员工"}}}, "example": [{"target_employee_id": "EMP002", "target_employee_name": "李四", "can_book": true, "permission_reason": "同部门员工"}]}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001", "booking_permissions": [{"target_employee_id": "EMP002", "target_employee_name": "李四", "can_book": true, "permission_reason": "同部门员工"}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_5": {"api_id": 5, "api_name": "获取出行人卡信息", "method": "POST", "endpoint": "/api/v1/traveler/cards", "description": "获取出行人卡信息接口，用于获取出行人的支付卡片信息", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"cards": {"type": "array", "description": "支付卡片信息列表", "items": {"type": "object", "properties": {"card_id": {"type": "string", "description": "卡片ID", "example": "CARD001"}, "card_type": {"type": "string", "description": "卡片类型", "enum": ["信用卡", "借记卡", "企业卡", "预付卡"], "example": "信用卡"}, "card_number": {"type": "string", "description": "卡号（脱敏显示）", "example": "****1234", "pattern": "\\*{4}\\d{4}"}, "card_holder": {"type": "string", "description": "持卡人姓名", "example": "张三"}, "issuing_bank": {"type": "string", "description": "发卡银行", "example": "招商银行"}, "is_default": {"type": "boolean", "description": "是否为默认卡片", "example": true}, "is_corporate": {"type": "boolean", "description": "是否为企业卡", "example": false}, "status": {"type": "string", "description": "卡片状态", "enum": ["active", "inactive", "expired"], "example": "active"}}}, "example": [{"card_id": "CARD001", "card_type": "信用卡", "card_number": "****1234", "card_holder": "张三", "issuing_bank": "招商银行", "is_default": true, "is_corporate": false, "status": "active"}]}, "pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"cards": [{"card_id": "CARD001", "card_type": "信用卡", "card_number": "****1234", "card_holder": "张三", "issuing_bank": "招商银行", "is_default": true, "is_corporate": false, "status": "active"}], "pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_6": {"api_id": 6, "api_name": "获取员工喜好", "method": "POST", "endpoint": "/api/v1/employee/preferences", "description": "获取员工喜好接口，用于获取员工的出行偏好设置", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_7": {"api_id": 7, "api_name": "获取员工是否高消费", "method": "POST", "endpoint": "/api/v1/employee/high-consumption", "description": "获取员工是否高消费接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"employee_id": {"type": "string", "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_8": {"api_id": 8, "api_name": "获取员工各品类是否管控差标", "method": "POST", "endpoint": "/api/v1/employee/category-controls", "description": "获取员工各品类是否管控差标接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"employee_id": {"type": "string", "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_9": {"api_id": 9, "api_name": "获取企业POI", "method": "POST", "endpoint": "/api/v1/enterprise/poi", "description": "获取企业POI接口，用于获取企业相关的地理位置信息", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"enterprise_id": {"type": "string", "required": true, "description": "企业唯一标识符", "example": "ENT001", "pattern": "^ENT[0-9]{3,}$"}}, "example": {"enterprise_id": "ENT001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"poi_list": {"type": "array", "description": "POI地址信息列表", "items": {"type": "object", "properties": {"poi_id": {"type": "string", "description": "POI唯一标识", "example": "POI001"}, "poi_name": {"type": "string", "description": "POI名称", "example": "公司总部"}, "poi_type": {"type": "string", "description": "POI类型", "enum": ["office", "hotel", "airport", "station", "other"], "example": "office"}, "address": {"type": "string", "description": "详细地址", "example": "北京市朝阳区xxx路xxx号"}, "city": {"type": "string", "description": "所在城市", "example": "北京"}, "coordinates": {"type": "object", "description": "坐标信息", "properties": {"longitude": {"type": "number", "description": "经度", "example": 116.397128}, "latitude": {"type": "number", "description": "纬度", "example": 39.916527}, "coordinate_system": {"type": "string", "description": "坐标系统", "enum": ["WGS84", "GCJ02", "BD09"], "example": "GCJ02"}}}}}, "example": [{"poi_id": "POI001", "poi_name": "公司总部", "poi_type": "office", "address": "北京市朝阳区xxx路xxx号", "city": "北京", "coordinates": {"longitude": 116.397128, "latitude": 39.916527, "coordinate_system": "GCJ02"}}]}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"poi_list": [{"poi_id": "POI001", "poi_name": "公司总部", "poi_type": "office", "address": "北京市朝阳区xxx路xxx号", "city": "北京", "coordinates": {"longitude": 116.397128, "latitude": 39.916527, "coordinate_system": "GCJ02"}}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_10": {"api_id": 10, "api_name": "获取企业参照人信息", "method": "POST", "endpoint": "/api/v1/enterprise/reference-person", "description": "获取企业参照人信息接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"enterprise_id": {"type": "string", "required": true, "description": "企业唯一标识符", "example": "ENT001", "pattern": "^ENT[0-9]{3,}$"}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"enterprise_id": "ENT001", "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_11": {"api_id": 11, "api_name": "获取出行人管控政策（预定管控）", "method": "POST", "endpoint": "/api/v1/出行人管控政策（预定管控）", "description": "获取出行人管控政策（预定管控）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"policies": {"type": "array", "description": "差旅政策列表", "items": {"type": "object", "properties": {"policy_id": {"type": "string", "description": "政策ID", "example": "POL001"}, "policy_name": {"type": "string", "description": "政策名称", "example": "标准差旅政策"}, "policy_type": {"type": "string", "description": "政策类型", "enum": ["flight", "hotel", "train", "taxi", "meal"], "example": "flight"}, "effective_date": {"type": "string", "format": "date", "description": "生效日期", "example": "2025-01-01"}, "expiry_date": {"type": "string", "format": "date", "description": "失效日期", "example": "2025-12-31"}, "rules": {"type": "object", "description": "政策规则", "properties": {"max_amount": {"type": "number", "description": "最大金额限制", "example": 5000.0}, "booking_advance_days": {"type": "integer", "description": "提前预订天数要求", "example": 3}, "approval_required": {"type": "boolean", "description": "是否需要审批", "example": true}}}}}, "example": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}]}, "order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}], "order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_12": {"api_id": 12, "api_name": "获取出行人有效出差申请单", "method": "POST", "endpoint": "/api/v1/出行人有效出差申请单", "description": "获取出行人有效出差申请单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"policies": {"type": "array", "description": "差旅政策列表", "items": {"type": "object", "properties": {"policy_id": {"type": "string", "description": "政策ID", "example": "POL001"}, "policy_name": {"type": "string", "description": "政策名称", "example": "标准差旅政策"}, "policy_type": {"type": "string", "description": "政策类型", "enum": ["flight", "hotel", "train", "taxi", "meal"], "example": "flight"}, "effective_date": {"type": "string", "format": "date", "description": "生效日期", "example": "2025-01-01"}, "expiry_date": {"type": "string", "format": "date", "description": "失效日期", "example": "2025-12-31"}, "rules": {"type": "object", "description": "政策规则", "properties": {"max_amount": {"type": "number", "description": "最大金额限制", "example": 5000.0}, "booking_advance_days": {"type": "integer", "description": "提前预订天数要求", "example": 3}, "approval_required": {"type": "boolean", "description": "是否需要审批", "example": true}}}}}, "example": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}]}, "order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}], "order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_13": {"api_id": 13, "api_name": "获取出行人管控政策简单（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人管控政策简单（第一版暂不实施）", "description": "获取出行人管控政策简单（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_14": {"api_id": 14, "api_name": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）", "description": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"policies": {"type": "array", "description": "差旅政策列表", "items": {"type": "object", "properties": {"policy_id": {"type": "string", "description": "政策ID", "example": "POL001"}, "policy_name": {"type": "string", "description": "政策名称", "example": "标准差旅政策"}, "policy_type": {"type": "string", "description": "政策类型", "enum": ["flight", "hotel", "train", "taxi", "meal"], "example": "flight"}, "effective_date": {"type": "string", "format": "date", "description": "生效日期", "example": "2025-01-01"}, "expiry_date": {"type": "string", "format": "date", "description": "失效日期", "example": "2025-12-31"}, "rules": {"type": "object", "description": "政策规则", "properties": {"max_amount": {"type": "number", "description": "最大金额限制", "example": 5000.0}, "booking_advance_days": {"type": "integer", "description": "提前预订天数要求", "example": 3}, "approval_required": {"type": "boolean", "description": "是否需要审批", "example": true}}}}}, "example": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}]}, "order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}], "order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_15": {"api_id": 15, "api_name": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）", "description": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"policies": {"type": "array", "description": "差旅政策列表", "items": {"type": "object", "properties": {"policy_id": {"type": "string", "description": "政策ID", "example": "POL001"}, "policy_name": {"type": "string", "description": "政策名称", "example": "标准差旅政策"}, "policy_type": {"type": "string", "description": "政策类型", "enum": ["flight", "hotel", "train", "taxi", "meal"], "example": "flight"}, "effective_date": {"type": "string", "format": "date", "description": "生效日期", "example": "2025-01-01"}, "expiry_date": {"type": "string", "format": "date", "description": "失效日期", "example": "2025-12-31"}, "rules": {"type": "object", "description": "政策规则", "properties": {"max_amount": {"type": "number", "description": "最大金额限制", "example": 5000.0}, "booking_advance_days": {"type": "integer", "description": "提前预订天数要求", "example": 3}, "approval_required": {"type": "boolean", "description": "是否需要审批", "example": true}}}}}, "example": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}]}, "order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}], "order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_16": {"api_id": 16, "api_name": "创建出差申请单（内）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/create/出差申请单（内）（第一版暂不实施）", "description": "创建出差申请单（内）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {}, "example": {}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_17": {"api_id": 17, "api_name": "创建出差申请单（外）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/create/出差申请单（外）（第一版暂不实施）", "description": "创建出差申请单（外）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"departure": {"type": "string", "required": true, "description": "出发地城市或地点", "example": "北京", "minLength": 2, "maxLength": 100}, "destination": {"type": "string", "required": true, "description": "目的地城市或地点", "example": "上海", "minLength": 2, "maxLength": 100}, "order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "policy_id": {"type": "string", "required": false, "description": "差旅政策ID", "example": "POL001", "pattern": "^POL[0-9]{3,}$"}}, "example": {"departure": "北京", "destination": "上海", "order_id": "TO2025073100001", "policy_id": "POL001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_18": {"api_id": 18, "api_name": "获取出行人当前位置", "method": "POST", "endpoint": "/api/v1/出行人当前位置", "description": "获取出行人当前位置接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_19": {"api_id": 19, "api_name": "获取员工产品预订权限", "method": "POST", "endpoint": "/api/v1/员工产品预订权限", "description": "获取员工产品预订权限接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_20": {"api_id": 20, "api_name": "获取出行人未出行订单-航班（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人未出行订单-航班（第一版暂不实施）", "description": "获取出行人未出行订单-航班（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_21": {"api_id": 21, "api_name": "获取出行人未出行订单-火车票（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人未出行订单-火车票（第一版暂不实施）", "description": "获取出行人未出行订单-火车票（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_22": {"api_id": 22, "api_name": "获取出行人未出行订单-酒店（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人未出行订单-酒店（第一版暂不实施）", "description": "获取出行人未出行订单-酒店（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"poi_list": {"type": "array", "description": "POI地址信息列表", "items": {"type": "object", "properties": {"poi_id": {"type": "string", "description": "POI唯一标识", "example": "POI001"}, "poi_name": {"type": "string", "description": "POI名称", "example": "公司总部"}, "poi_type": {"type": "string", "description": "POI类型", "enum": ["office", "hotel", "airport", "station", "other"], "example": "office"}, "address": {"type": "string", "description": "详细地址", "example": "北京市朝阳区xxx路xxx号"}, "city": {"type": "string", "description": "所在城市", "example": "北京"}, "coordinates": {"type": "object", "description": "坐标信息", "properties": {"longitude": {"type": "number", "description": "经度", "example": 116.397128}, "latitude": {"type": "number", "description": "纬度", "example": 39.916527}, "coordinate_system": {"type": "string", "description": "坐标系统", "enum": ["WGS84", "GCJ02", "BD09"], "example": "GCJ02"}}}}}, "example": [{"poi_id": "POI001", "poi_name": "公司总部", "poi_type": "office", "address": "北京市朝阳区xxx路xxx号", "city": "北京", "coordinates": {"longitude": 116.397128, "latitude": 39.916527, "coordinate_system": "GCJ02"}}]}, "order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"poi_list": [{"poi_id": "POI001", "poi_name": "公司总部", "poi_type": "office", "address": "北京市朝阳区xxx路xxx号", "city": "北京", "coordinates": {"longitude": 116.397128, "latitude": 39.916527, "coordinate_system": "GCJ02"}}], "order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_23": {"api_id": 23, "api_name": "获取出行人未出行订单-用车（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人未出行订单-用车（第一版暂不实施）", "description": "获取出行人未出行订单-用车（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_24": {"api_id": 24, "api_name": "获取出行人未使用机票（OPEN票）", "method": "POST", "endpoint": "/api/v1/出行人未使用机票（open票）", "description": "获取出行人未使用机票（OPEN票）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_25": {"api_id": 25, "api_name": "获取出行人出发地用车与目的地用车历史订单", "method": "POST", "endpoint": "/api/v1/出行人出发地用车与目的地用车历史订单", "description": "获取出行人出发地用车与目的地用车历史订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_26": {"api_id": 26, "api_name": "获取出行人所属公司授信/预存剩余额度是否>0", "method": "POST", "endpoint": "/api/v1/出行人所属公司授信/预存剩余额度是否>0", "description": "获取出行人所属公司授信/预存剩余额度是否>0接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {}, "example": {}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_27": {"api_id": 27, "api_name": "获取行程方案中地点与时间跨度的天气", "method": "POST", "endpoint": "/api/v1/行程方案中地点与时间跨度的天气", "description": "获取行程方案中地点与时间跨度的天气接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {}, "example": {}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_28": {"api_id": 28, "api_name": "获取出行人历史相同行程订单-火车票", "method": "POST", "endpoint": "/api/v1/出行人历史相同行程订单-火车票", "description": "获取出行人历史相同行程订单-火车票接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_29": {"api_id": 29, "api_name": "获取出行人历史相同行程订单-机票", "method": "POST", "endpoint": "/api/v1/出行人历史相同行程订单-机票", "description": "获取出行人历史相同行程订单-机票接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_30": {"api_id": 30, "api_name": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）", "description": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_31": {"api_id": 31, "api_name": "获取航班搜索列表（需要包含舱位与可购保险信息）", "method": "POST", "endpoint": "/api/v1/航班搜索列表（需要包含舱位与可购保险信息）", "description": "获取航班搜索列表（需要包含舱位与可购保险信息）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"], "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_32": {"api_id": 32, "api_name": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）", "method": "POST", "endpoint": "/api/v1/车次搜索列表（需要包含各座位类型余票与可购保险信息）", "description": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"], "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_33": {"api_id": 33, "api_name": "获取精选酒店列表", "method": "POST", "endpoint": "/api/v1/精选酒店列表", "description": "获取精选酒店列表接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_34": {"api_id": 34, "api_name": "获取酒店搜索列表", "method": "POST", "endpoint": "/api/v1/酒店搜索列表", "description": "获取酒店搜索列表接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"], "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_35": {"api_id": 35, "api_name": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）", "method": "POST", "endpoint": "/api/v1/酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）", "description": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"policies": {"type": "array", "description": "差旅政策列表", "items": {"type": "object", "properties": {"policy_id": {"type": "string", "description": "政策ID", "example": "POL001"}, "policy_name": {"type": "string", "description": "政策名称", "example": "标准差旅政策"}, "policy_type": {"type": "string", "description": "政策类型", "enum": ["flight", "hotel", "train", "taxi", "meal"], "example": "flight"}, "effective_date": {"type": "string", "format": "date", "description": "生效日期", "example": "2025-01-01"}, "expiry_date": {"type": "string", "format": "date", "description": "失效日期", "example": "2025-12-31"}, "rules": {"type": "object", "description": "政策规则", "properties": {"max_amount": {"type": "number", "description": "最大金额限制", "example": 5000.0}, "booking_advance_days": {"type": "integer", "description": "提前预订天数要求", "example": 3}, "approval_required": {"type": "boolean", "description": "是否需要审批", "example": true}}}}}, "example": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}]}, "pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "policy_type": "flight", "effective_date": "2025-01-01", "expiry_date": "2025-12-31", "rules": {"max_amount": 5000.0, "booking_advance_days": 3, "approval_required": true}}], "pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_36": {"api_id": 36, "api_name": "获取用车列表（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/用车列表（第一版暂不实施）", "description": "获取用车列表（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}, "departure": {"type": "string", "required": true, "description": "出发地城市或地点", "example": "北京", "minLength": 2, "maxLength": 100}, "departure_time": {"type": "string", "format": "datetime", "required": true, "description": "计划出发时间", "example": "2025-08-15 09:00:00", "pattern": "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$"}, "page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"], "departure": "北京", "departure_time": "2025-08-15 09:00:00", "page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_37": {"api_id": 37, "api_name": "获取出行人出差申请单状态", "method": "POST", "endpoint": "/api/v1/出行人出差申请单状态", "description": "获取出行人出差申请单状态接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_38": {"api_id": 38, "api_name": "获取出行人行程提交项启用状态", "method": "POST", "endpoint": "/api/v1/出行人行程提交项启用状态", "description": "获取出行人行程提交项启用状态接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"employee_id": {"type": "string", "required": true, "description": "员工唯一标识符", "example": "EMP001", "pattern": "^EMP[0-9]{3,}$", "minLength": 6, "maxLength": 20}, "traveler_employee_ids": {"type": "array", "items": {"type": "string", "pattern": "^EMP[0-9]{3,}$"}, "required": false, "description": "出行人员工ID列表，支持多个出行人", "example": ["EMP001", "EMP002"], "minItems": 1, "maxItems": 10}}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_39": {"api_id": 39, "api_name": "获取结算金额信息", "method": "POST", "endpoint": "/api/v1/结算金额信息", "description": "获取结算金额信息接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"page": {"type": "integer", "required": false, "description": "页码，从1开始", "example": 1, "minimum": 1, "default": 1}, "page_size": {"type": "integer", "required": false, "description": "每页数量", "example": 20, "minimum": 1, "maximum": 100, "default": 20}}, "example": {"page": 1, "page_size": 20}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"pagination": {"type": "object", "description": "分页信息", "properties": {"current_page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}, "total_count": {"type": "integer", "description": "总记录数", "example": 150}, "total_pages": {"type": "integer", "description": "总页数", "example": 8}, "has_next": {"type": "boolean", "description": "是否有下一页", "example": true}, "has_prev": {"type": "boolean", "description": "是否有上一页", "example": false}}, "example": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"pagination": {"current_page": 1, "page_size": 20, "total_count": 150, "total_pages": 8, "has_next": true, "has_prev": false}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_40": {"api_id": 40, "api_name": "提交订单（支持多品类多订单）", "method": "POST", "endpoint": "/api/v1/提交订单（支持多品类多订单）", "description": "提交订单（支持多品类多订单）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"policy_id": {"type": "string", "required": false, "description": "差旅政策ID", "example": "POL001", "pattern": "^POL[0-9]{3,}$"}}, "example": {"policy_id": "POL001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_41": {"api_id": 41, "api_name": "申请改签机票订单", "method": "POST", "endpoint": "/api/v1/申请改签机票订单", "description": "申请改签机票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_42": {"api_id": 42, "api_name": "确认改签机票订单", "method": "POST", "endpoint": "/api/v1/确认改签机票订单", "description": "确认改签机票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_43": {"api_id": 43, "api_name": "变更酒店订单", "method": "POST", "endpoint": "/api/v1/变更酒店订单", "description": "变更酒店订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_44": {"api_id": 44, "api_name": "验证是否可改火车票订单", "method": "POST", "endpoint": "/api/v1/验证是否可改火车票订单", "description": "验证是否可改火车票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_45": {"api_id": 45, "api_name": "申请改签火车票订单", "method": "POST", "endpoint": "/api/v1/申请改签火车票订单", "description": "申请改签火车票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_46": {"api_id": 46, "api_name": "确认改签火车票订单", "method": "POST", "endpoint": "/api/v1/确认改签火车票订单", "description": "确认改签火车票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_47": {"api_id": 47, "api_name": "12306账号登录", "method": "POST", "endpoint": "/api/v1/12306账号登录", "description": "12306账号登录接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_48": {"api_id": 48, "api_name": "12306常旅客信息查询", "method": "POST", "endpoint": "/api/v1/12306常旅客信息查询", "description": "12306常旅客信息查询接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {}, "example": {}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"employee_name": {"type": "string", "description": "员工姓名", "example": "张三", "minLength": 2, "maxLength": 50}, "documents": {"type": "array", "description": "证件信息列表", "items": {"type": "object", "properties": {"document_type": {"type": "string", "description": "证件类型", "enum": ["身份证", "护照", "港澳通行证", "台湾通行证", "驾驶证"], "example": "身份证"}, "document_number": {"type": "string", "description": "证件号码", "example": "110101199001011234"}, "expiry_date": {"type": "string", "format": "date", "description": "有效期至", "example": "2030-12-31"}, "issuing_authority": {"type": "string", "description": "发证机关", "example": "北京市公安局"}}}, "example": [{"document_type": "身份证", "document_number": "110101199001011234", "expiry_date": "2030-12-31", "issuing_authority": "北京市公安局"}]}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"employee_name": "张三", "documents": [{"document_type": "身份证", "document_number": "110101199001011234", "expiry_date": "2030-12-31", "issuing_authority": "北京市公安局"}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_49": {"api_id": 49, "api_name": "提交用车订单（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/提交用车订单（第一版暂不实施）", "description": "提交用车订单（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {}, "example": {}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_50": {"api_id": 50, "api_name": "提交火车票出票申请", "method": "POST", "endpoint": "/api/v1/提交火车票出票申请", "description": "提交火车票出票申请接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_51": {"api_id": 51, "api_name": "提交机票出票申请", "method": "POST", "endpoint": "/api/v1/提交机票出票申请", "description": "提交机票出票申请接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {"order_id": {"type": "string", "required": true, "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}}, "example": {"order_id": "TO2025073100001"}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}, "api_52": {"api_id": 52, "api_name": "提交保险订单", "method": "POST", "endpoint": "/api/v1/提交保险订单", "description": "提交保险订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": {"type": "string", "required": true, "description": "请求内容类型", "example": "application/json"}, "Authorization": {"type": "string", "required": true, "description": "Bearer认证令牌", "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "X-Enterprise-ID": {"type": "string", "required": true, "description": "企业唯一标识", "example": "ENT001"}}, "body": {}, "example": {}}, "response": {"success": {"schema": {"code": {"type": "integer", "description": "响应状态码", "example": 200, "enum": [200]}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"order_id": {"type": "string", "description": "差旅单唯一标识", "example": "TO2025073100001", "pattern": "^TO\\d{13}$"}, "order_status": {"type": "string", "description": "差旅单状态", "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"], "example": "submitted"}, "create_time": {"type": "string", "format": "datetime", "description": "创建时间", "example": "2025-07-31 14:30:25"}, "update_time": {"type": "string", "format": "datetime", "description": "更新时间", "example": "2025-07-31 14:30:25"}}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳", "example": "2025-07-31 14:30:25"}}, "example": {"code": 200, "message": "success", "data": {"order_id": "TO2025073100001", "order_status": "submitted", "create_time": "2025-07-31 14:30:25", "update_time": "2025-07-31 14:30:25"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"schema": {"code": {"type": "integer", "description": "错误状态码", "enum": [400, 401, 403, 404, 500]}, "message": {"type": "string", "description": "错误消息"}, "data": {"type": "null", "description": "错误时数据为空"}, "timestamp": {"type": "string", "format": "datetime", "description": "响应时间戳"}}, "examples": {"400": {"code": 400, "message": "请求参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}, "401": {"code": 401, "message": "未授权访问", "data": null, "timestamp": "2025-07-31 14:30:25"}, "500": {"code": 500, "message": "服务器内部错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}}