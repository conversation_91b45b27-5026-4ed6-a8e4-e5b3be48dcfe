{"api_1": {"api_id": 1, "api_name": "员工信息同步（预订人）", "method": "POST", "endpoint": "/api/v1/employee/sync", "description": "员工信息同步（预订人）接口，用于同步和获取员工基本信息", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "sso_token": "string - 单点登录令牌"}, "example": {"employee_id": "EMP001", "sso_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"employee_id": "string - 员工唯一标识", "enterprise_id": "string - 企业唯一标识", "is_express_booking": "boolean - 是否极速预订员工"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001", "enterprise_id": "ENT001", "is_express_booking": true}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_2": {"api_id": 2, "api_name": "获取员工代订类别", "method": "POST", "endpoint": "/api/v1/employee/booking-types", "description": "获取员工代订类别接口，用于获取员工可代订的人员类别范围", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"booking_types": "array<string> - 代订类别列表"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"booking_types": ["本人", "同事-所有员工"]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_3": {"api_id": 3, "api_name": "获取出行人详细信息", "method": "POST", "endpoint": "/api/v1/traveler/details", "description": "获取出行人详细信息接口，用于获取指定出行人的详细个人信息", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_name": "string - 出行人姓名", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_name": "张三", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"employee_id": "string - 员工唯一标识", "organization": "object - 组织架构信息", "vip_level": "string - VIP等级 (NONE/VIP1/VIP2/VIP3)", "contact": "object - 联系方式信息"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001", "organization": {"department": "技术部", "team": "后端开发组", "level": "高级工程师"}, "vip_level": "VIP1", "contact": {"phone": "13800138000", "email": "zhang<PERSON>@company.com"}}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_4": {"api_id": 4, "api_name": "是否在代订范围（判断出行人是否在预订人的代订范围）", "method": "POST", "endpoint": "/api/v1/booking/permission-check", "description": "是否在代订范围（判断出行人是否在预订人的代订范围）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"employee_id": "string - 员工唯一标识", "booking_permission": "boolean - 是否在可订范围"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001", "booking_permission": true}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_5": {"api_id": 5, "api_name": "获取出行人卡信息", "method": "POST", "endpoint": "/api/v1/traveler/cards", "description": "获取出行人卡信息接口，用于获取出行人的支付卡片信息", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"card_type": "string - 卡片类型", "card_number": "string - 卡号", "issuing_institution": "string - 发卡机构"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"card_type": "信用卡", "card_number": "****1234", "issuing_institution": "招商银行"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_6": {"api_id": 6, "api_name": "获取员工喜好", "method": "POST", "endpoint": "/api/v1/employee/preferences", "description": "获取员工喜好接口，用于获取员工的出行偏好设置", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_7": {"api_id": 7, "api_name": "获取员工是否高消费", "method": "POST", "endpoint": "/api/v1/employee/high-consumption", "description": "获取员工是否高消费接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"employee_id": "string - 员工唯一标识"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_8": {"api_id": 8, "api_name": "获取员工各品类是否管控差标", "method": "POST", "endpoint": "/api/v1/employee/category-controls", "description": "获取员工各品类是否管控差标接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"employee_id": "string - 员工唯一标识"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"employee_id": "EMP001"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_9": {"api_id": 9, "api_name": "获取企业POI", "method": "POST", "endpoint": "/api/v1/enterprise/poi", "description": "获取企业POI接口，用于获取企业相关的地理位置信息", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"enterprise_id": "string - 企业唯一标识"}, "example": {"enterprise_id": "ENT001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"poi_list": "array<object> - POI地址列表", "coordinates": "object - 坐标信息"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"poi_list": [{"name": "公司总部", "address": "北京市朝阳区xxx路xxx号", "coordinates": {"longitude": 116.397128, "latitude": 39.916527}}], "coordinates": "sample_value"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_10": {"api_id": 10, "api_name": "获取企业参照人信息", "method": "POST", "endpoint": "/api/v1/enterprise/reference-person", "description": "获取企业参照人信息接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"enterprise_id": "string - 企业唯一标识"}, "example": {"enterprise_id": "ENT001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_11": {"api_id": 11, "api_name": "获取出行人管控政策（预定管控）", "method": "POST", "endpoint": "/api/v1/get/出行人管控政策（预定管控）", "description": "获取出行人管控政策（预定管控）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"policies": "array<object> - 政策列表", "expense_standards": "object - 差旅标准"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "effective_date": "2025-01-01"}], "expense_standards": "sample_value"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_12": {"api_id": 12, "api_name": "获取出行人有效出差申请单", "method": "POST", "endpoint": "/api/v1/get/出行人有效出差申请单", "description": "获取出行人有效出差申请单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"policies": "array<object> - 政策列表"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "effective_date": "2025-01-01"}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_13": {"api_id": 13, "api_name": "获取出行人管控政策简单（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人管控政策简单（第一版暂不实施）", "description": "获取出行人管控政策简单（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_14": {"api_id": 14, "api_name": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）", "description": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"policies": "array<object> - 政策列表"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "effective_date": "2025-01-01"}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_15": {"api_id": 15, "api_name": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）", "description": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"policies": "array<object> - 政策列表"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "effective_date": "2025-01-01"}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_16": {"api_id": 16, "api_name": "创建出差申请单（内）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/create/出差申请单（内）（第一版暂不实施）", "description": "创建出差申请单（内）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_17": {"api_id": 17, "api_name": "创建出差申请单（外）（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/create/出差申请单（外）（第一版暂不实施）", "description": "创建出差申请单（外）（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"order_id": "string - 差旅单ID", "policy_id": "string - 政策ID", "departure": "string - 出发地", "destination": "string - 目的地"}, "example": {"order_id": "TO2025073100001", "policy_id": "POL001", "departure": "北京", "destination": "上海"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_18": {"api_id": 18, "api_name": "获取出行人当前位置", "method": "POST", "endpoint": "/api/v1/get/出行人当前位置", "description": "获取出行人当前位置接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"coordinates": "object - 坐标信息"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"coordinates": "sample_value"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_19": {"api_id": 19, "api_name": "获取员工产品预订权限", "method": "POST", "endpoint": "/api/v1/get/员工产品预订权限", "description": "获取员工产品预订权限接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_20": {"api_id": 20, "api_name": "获取出行人未出行订单-航班（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人未出行订单-航班（第一版暂不实施）", "description": "获取出行人未出行订单-航班（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_21": {"api_id": 21, "api_name": "获取出行人未出行订单-火车票（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人未出行订单-火车票（第一版暂不实施）", "description": "获取出行人未出行订单-火车票（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_22": {"api_id": 22, "api_name": "获取出行人未出行订单-酒店（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人未出行订单-酒店（第一版暂不实施）", "description": "获取出行人未出行订单-酒店（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"poi_list": "array<object> - POI地址列表"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"poi_list": [{"name": "公司总部", "address": "北京市朝阳区xxx路xxx号", "coordinates": {"longitude": 116.397128, "latitude": 39.916527}}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_23": {"api_id": 23, "api_name": "获取出行人未出行订单-用车（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人未出行订单-用车（第一版暂不实施）", "description": "获取出行人未出行订单-用车（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"coordinates": "object - 坐标信息"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"coordinates": "sample_value"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_24": {"api_id": 24, "api_name": "获取出行人未使用机票（OPEN票）", "method": "POST", "endpoint": "/api/v1/get/出行人未使用机票（open票）", "description": "获取出行人未使用机票（OPEN票）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_25": {"api_id": 25, "api_name": "获取出行人出发地用车与目的地用车历史订单", "method": "POST", "endpoint": "/api/v1/get/出行人出发地用车与目的地用车历史订单", "description": "获取出行人出发地用车与目的地用车历史订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"coordinates": "object - 坐标信息"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"coordinates": "sample_value"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_26": {"api_id": 26, "api_name": "获取出行人所属公司授信/预存剩余额度是否>0", "method": "POST", "endpoint": "/api/v1/get/出行人所属公司授信/预存剩余额度是否>0", "description": "获取出行人所属公司授信/预存剩余额度是否>0接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_27": {"api_id": 27, "api_name": "获取行程方案中地点与时间跨度的天气", "method": "POST", "endpoint": "/api/v1/get/行程方案中地点与时间跨度的天气", "description": "获取行程方案中地点与时间跨度的天气接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_28": {"api_id": 28, "api_name": "获取出行人历史相同行程订单-火车票", "method": "POST", "endpoint": "/api/v1/get/出行人历史相同行程订单-火车票", "description": "获取出行人历史相同行程订单-火车票接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_29": {"api_id": 29, "api_name": "获取出行人历史相同行程订单-机票", "method": "POST", "endpoint": "/api/v1/get/出行人历史相同行程订单-机票", "description": "获取出行人历史相同行程订单-机票接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_30": {"api_id": 30, "api_name": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）", "description": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_31": {"api_id": 31, "api_name": "获取航班搜索列表（需要包含舱位与可购保险信息）", "method": "POST", "endpoint": "/api/v1/get/航班搜索列表（需要包含舱位与可购保险信息）", "description": "获取航班搜索列表（需要包含舱位与可购保险信息）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_32": {"api_id": 32, "api_name": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）", "method": "POST", "endpoint": "/api/v1/get/车次搜索列表（需要包含各座位类型余票与可购保险信息）", "description": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_33": {"api_id": 33, "api_name": "获取精选酒店列表", "method": "POST", "endpoint": "/api/v1/get/精选酒店列表", "description": "获取精选酒店列表接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识"}, "example": {"employee_id": "EMP001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_34": {"api_id": 34, "api_name": "获取酒店搜索列表", "method": "POST", "endpoint": "/api/v1/get/酒店搜索列表", "description": "获取酒店搜索列表接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_35": {"api_id": 35, "api_name": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）", "method": "POST", "endpoint": "/api/v1/get/酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）", "description": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"policies": "array<object> - 政策列表"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"policies": [{"policy_id": "POL001", "policy_name": "标准差旅政策", "effective_date": "2025-01-01"}]}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_36": {"api_id": 36, "api_name": "获取用车列表（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/get/用车列表（第一版暂不实施）", "description": "获取用车列表（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表", "departure": "string - 出发地", "departure_time": "string - 出发时间 (YYYY-MM-DD HH:mm:ss)"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"], "departure": "北京", "departure_time": "2025-08-15 09:00:00"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_37": {"api_id": 37, "api_name": "获取出行人出差申请单状态", "method": "POST", "endpoint": "/api/v1/get/出行人出差申请单状态", "description": "获取出行人出差申请单状态接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_38": {"api_id": 38, "api_name": "获取出行人行程提交项启用状态", "method": "POST", "endpoint": "/api/v1/get/出行人行程提交项启用状态", "description": "获取出行人行程提交项启用状态接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"employee_id": "string - 员工唯一标识", "traveler_employee_ids": "array<string> - 出行人员工ID列表"}, "example": {"employee_id": "EMP001", "traveler_employee_ids": ["EMP001", "EMP002"]}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_39": {"api_id": 39, "api_name": "获取结算金额信息", "method": "POST", "endpoint": "/api/v1/get/结算金额信息", "description": "获取结算金额信息接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_40": {"api_id": 40, "api_name": "提交订单（支持多品类多订单）", "method": "POST", "endpoint": "/api/v1/提交订单（支持多品类多订单）", "description": "提交订单（支持多品类多订单）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {"policy_id": "string - 政策ID"}, "example": {"policy_id": "POL001"}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_41": {"api_id": 41, "api_name": "申请改签机票订单", "method": "POST", "endpoint": "/api/v1/申请改签机票订单", "description": "申请改签机票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_42": {"api_id": 42, "api_name": "确认改签机票订单", "method": "POST", "endpoint": "/api/v1/确认改签机票订单", "description": "确认改签机票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_43": {"api_id": 43, "api_name": "变更酒店订单", "method": "POST", "endpoint": "/api/v1/变更酒店订单", "description": "变更酒店订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_44": {"api_id": 44, "api_name": "验证是否可改火车票订单", "method": "POST", "endpoint": "/api/v1/验证是否可改火车票订单", "description": "验证是否可改火车票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_45": {"api_id": 45, "api_name": "申请改签火车票订单", "method": "POST", "endpoint": "/api/v1/申请改签火车票订单", "description": "申请改签火车票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_46": {"api_id": 46, "api_name": "确认改签火车票订单", "method": "POST", "endpoint": "/api/v1/确认改签火车票订单", "description": "确认改签火车票订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_47": {"api_id": 47, "api_name": "12306账号登录", "method": "POST", "endpoint": "/api/v1/12306账号登录", "description": "12306账号登录接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_48": {"api_id": 48, "api_name": "12306常旅客信息查询", "method": "POST", "endpoint": "/api/v1/12306常旅客信息查询", "description": "12306常旅客信息查询接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {"employee_name": "string - 员工姓名"}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {"employee_name": "张三"}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_49": {"api_id": 49, "api_name": "提交用车订单（第一版暂不实施）", "method": "POST", "endpoint": "/api/v1/提交用车订单（第一版暂不实施）", "description": "提交用车订单（第一版暂不实施）接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_50": {"api_id": 50, "api_name": "提交火车票出票申请", "method": "POST", "endpoint": "/api/v1/提交火车票出票申请", "description": "提交火车票出票申请接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_51": {"api_id": 51, "api_name": "提交机票出票申请", "method": "POST", "endpoint": "/api/v1/提交机票出票申请", "description": "提交机票出票申请接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}, "api_52": {"api_id": 52, "api_name": "提交保险订单", "method": "POST", "endpoint": "/api/v1/提交保险订单", "description": "提交保险订单接口，用于相关业务处理", "request": {"headers": {"Content-Type": "application/json", "Authorization": "Bearer {access_token}", "X-Enterprise-ID": "{enterprise_id}"}, "body_schema": {}, "example": {}}, "response": {"success": {"schema": {"code": "int - 状态码", "message": "string - 响应消息", "data": {}, "timestamp": "string - 响应时间"}, "example": {"code": 200, "message": "success", "data": {}, "timestamp": "2025-07-31 14:30:25"}}, "error": {"example": {"code": 400, "message": "参数错误", "data": null, "timestamp": "2025-07-31 14:30:25"}}}}}