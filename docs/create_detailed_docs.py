import json
from datetime import datetime

def create_enhanced_api_docs():
    """Create enhanced HTML documentation with detailed JSON examples"""
    
    # Load API data and specifications
    with open('api_data.json', 'r', encoding='utf-8') as f:
        api_data = json.load(f)
    
    with open('api_json_specs.json', 'r', encoding='utf-8') as f:
        api_specs = json.load(f)
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Pilot API 接口文档 - 详细版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .sidebar {
            position: fixed;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 280px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .sidebar ul {
            list-style: none;
        }
        
        .sidebar li {
            margin-bottom: 8px;
        }
        
        .sidebar a {
            text-decoration: none;
            color: #666;
            font-size: 14px;
            display: block;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .sidebar a:hover {
            background-color: #f0f2ff;
            color: #667eea;
        }
        
        .content {
            margin-left: 320px;
        }
        
        .api-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .api-section h2 {
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f2ff;
        }
        
        .api-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .api-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .api-header:hover {
            background: #f0f2ff;
        }
        
        .api-title {
            font-weight: bold;
            color: #495057;
            margin-left: 10px;
        }
        
        .api-id {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .api-method {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .api-priority {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .priority-high {
            background: #ff6b6b;
            color: white;
        }
        
        .priority-medium {
            background: #ffd93d;
            color: #333;
        }
        
        .priority-low {
            background: #6bcf7f;
            color: white;
        }
        
        .api-content {
            padding: 25px;
            display: none;
        }
        
        .api-content.active {
            display: block;
        }
        
        .section-tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .endpoint-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .endpoint-info code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        .json-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .json-header {
            background: #e9ecef;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 14px;
            color: #495057;
        }
        
        .json-content {
            padding: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .param-table th,
        .param-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .param-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .param-table code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .meta-info {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #666;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .search-box {
            margin-bottom: 20px;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .toggle-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 18px;
        }
        
        .workflow-count {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 5px;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #667eea;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-btn:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 1200px) {
            .sidebar {
                display: none;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Travel Pilot API 接口文档</h1>
            <p>企业差旅管理系统接口规范 - 共 ''' + str(len(api_data)) + ''' 个接口 (详细版)</p>
        </div>
        
        <div class="sidebar">
            <h3>接口导航</h3>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索接口...">
            </div>
            <ul id="apiNavigation"></ul>
        </div>
        
        <div class="content">
            <div id="apiList"></div>
        </div>
    </div>

    <script>
        const apiData = ''' + json.dumps(api_data, ensure_ascii=False, indent=8) + ''';
        const apiSpecs = ''' + json.dumps(api_specs, ensure_ascii=False, indent=8) + ''';
        
        // Group APIs by workflow
        function groupApisByWorkflow() {
            const workflows = {};
            apiData.forEach(api => {
                const workflow = api['所属流程'] || '其他';
                if (!workflows[workflow]) {
                    workflows[workflow] = [];
                }
                workflows[workflow].push(api);
            });
            return workflows;
        }
        
        // Generate navigation
        function generateNavigation() {
            const navigation = document.getElementById('apiNavigation');
            const workflows = groupApisByWorkflow();
            
            Object.keys(workflows).sort().forEach(workflow => {
                const li = document.createElement('li');
                li.innerHTML = `<a href="#workflow-${workflow}">${workflow}<span class="workflow-count">${workflows[workflow].length}</span></a>`;
                navigation.appendChild(li);
            });
        }
        
        // Generate API list with detailed JSON examples
        function generateApiList() {
            const apiList = document.getElementById('apiList');
            const workflows = groupApisByWorkflow();
            
            Object.keys(workflows).sort().forEach(workflow => {
                const section = document.createElement('div');
                section.className = 'api-section';
                section.id = `workflow-${workflow}`;
                
                section.innerHTML = `
                    <h2>📋 ${workflow} (${workflows[workflow].length}个接口)</h2>
                    ${workflows[workflow].map(api => generateDetailedApiItem(api)).join('')}
                `;
                
                apiList.appendChild(section);
            });
        }
        
        // Generate detailed API item with JSON examples
        function generateDetailedApiItem(api) {
            const priorityClass = api['优先级'] === '高' ? 'priority-high' : 
                                api['优先级'] === '中' ? 'priority-medium' : 'priority-low';
            
            const apiId = `api-${api['接口编号']}`;
            const specKey = `api_${api['接口编号']}`;
            const spec = apiSpecs[specKey];
            
            if (!spec) return '';
            
            return `
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('${apiId}')">
                        <div>
                            <span class="api-id">API-${api['接口编号']}</span>
                            <span class="api-method">${spec.method}</span>
                            <span class="api-title">${api['接口名称']}</span>
                        </div>
                        <div>
                            <span class="api-priority ${priorityClass}">${api['优先级']}优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="${apiId}">
                        <div class="endpoint-info">
                            <p><strong>接口地址:</strong> <code>${spec.method} ${spec.endpoint}</code></p>
                            <p><strong>接口描述:</strong> ${spec.description}</p>
                        </div>
                        
                        <div class="section-tabs">
                            <div class="tab active" onclick="switchTab(event, '${apiId}-overview')">概览</div>
                            <div class="tab" onclick="switchTab(event, '${apiId}-request')">请求参数</div>
                            <div class="tab" onclick="switchTab(event, '${apiId}-response')">响应参数</div>
                            <div class="tab" onclick="switchTab(event, '${apiId}-examples')">示例</div>
                        </div>
                        
                        <div id="${apiId}-overview" class="tab-content active">
                            <h4>📋 接口概览</h4>
                            <table class="param-table">
                                <tr><td><strong>接口名称</strong></td><td>${api['接口名称']}</td></tr>
                                <tr><td><strong>请求方法</strong></td><td><code>${spec.method}</code></td></tr>
                                <tr><td><strong>接口地址</strong></td><td><code>${spec.endpoint}</code></td></tr>
                                <tr><td><strong>所属流程</strong></td><td>${api['所属流程']}</td></tr>
                                <tr><td><strong>优先级</strong></td><td>${api['优先级']}</td></tr>
                                <tr><td><strong>计划交付</strong></td><td>${api['计划交付时间']}</td></tr>
                            </table>
                        </div>
                        
                        <div id="${apiId}-request" class="tab-content">
                            <h4>📤 请求参数</h4>
                            <h5>Headers</h5>
                            <table class="param-table">
                                <thead>
                                    <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td><code>Content-Type</code></td><td>string</td><td>是</td><td>application/json</td></tr>
                                    <tr><td><code>Authorization</code></td><td>string</td><td>是</td><td>Bearer {access_token}</td></tr>
                                    <tr><td><code>X-Enterprise-ID</code></td><td>string</td><td>是</td><td>企业ID</td></tr>
                                </tbody>
                            </table>
                            
                            <h5>Body Parameters</h5>
                            ${generateParamTable(spec.request.body_schema)}
                        </div>
                        
                        <div id="${apiId}-response" class="tab-content">
                            <h4>📥 响应参数</h4>
                            <h5>成功响应</h5>
                            ${generateParamTable(spec.response.success.schema)}
                        </div>
                        
                        <div id="${apiId}-examples" class="tab-content">
                            <h4>💡 请求示例</h4>
                            ${generateJsonExample('请求示例', JSON.stringify(spec.request.example, null, 2))}
                            
                            <h4>✅ 成功响应示例</h4>
                            ${generateJsonExample('成功响应', JSON.stringify(spec.response.success.example, null, 2))}
                            
                            <h4>❌ 错误响应示例</h4>
                            ${generateJsonExample('错误响应', JSON.stringify(spec.response.error.example, null, 2))}
                        </div>
                        
                        <div class="meta-info">
                            <div class="meta-item">
                                <span>🎯</span>
                                <span>所属流程: ${api['所属流程']}</span>
                            </div>
                            <div class="meta-item">
                                <span>📅</span>
                                <span>计划交付: ${api['计划交付时间']}</span>
                            </div>
                            <div class="meta-item">
                                <span>⚡</span>
                                <span>优先级: ${api['优先级']}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Generate parameter table
        function generateParamTable(schema) {
            if (!schema || Object.keys(schema).length === 0) {
                return '<p>无参数</p>';
            }
            
            let table = `
                <table class="param-table">
                    <thead>
                        <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                    </thead>
                    <tbody>
            `;
            
            Object.entries(schema).forEach(([key, desc]) => {
                const [type, description] = desc.split(' - ');
                table += `<tr><td><code>${key}</code></td><td>${type}</td><td>是</td><td>${description || ''}</td></tr>`;
            });
            
            table += '</tbody></table>';
            return table;
        }
        
        // Generate JSON example
        function generateJsonExample(title, jsonContent) {
            const id = 'json_' + Math.random().toString(36).substr(2, 9);
            return `
                <div class="json-example">
                    <div class="json-header">
                        ${title}
                        <button class="copy-btn" onclick="copyToClipboard('${id}')" title="复制代码">复制</button>
                    </div>
                    <div class="json-content" id="${id}">${escapeHtml(jsonContent)}</div>
                </div>
            `;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Toggle API content visibility
        function toggleApi(apiId) {
            const content = document.getElementById(apiId);
            const isActive = content.classList.contains('active');
            
            // Close all other API contents
            document.querySelectorAll('.api-content.active').forEach(el => {
                el.classList.remove('active');
                el.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▼';
            });
            
            // Toggle current API content
            if (!isActive) {
                content.classList.add('active');
                content.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▲';
            }
        }
        
        // Switch tabs
        function switchTab(event, tabId) {
            const clickedTab = event.target;
            const apiContent = clickedTab.closest('.api-content');
            
            // Remove active class from all tabs and tab contents in this API
            apiContent.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            apiContent.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            clickedTab.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }
        
        // Copy to clipboard
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                // Show temporary feedback
                const button = element.parentElement.querySelector('.copy-btn');
                const originalText = button.textContent;
                button.textContent = '已复制!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            });
        }
        
        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const apiItems = document.querySelectorAll('.api-item');
                
                apiItems.forEach(item => {
                    const title = item.querySelector('.api-title').textContent.toLowerCase();
                    const content = item.textContent.toLowerCase();
                    
                    if (title.includes(query) || content.includes(query)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Show/hide sections based on visible items
                const sections = document.querySelectorAll('.api-section');
                sections.forEach(section => {
                    const visibleItems = section.querySelectorAll('.api-item[style="display: block;"], .api-item:not([style*="display: none"])');
                    if (visibleItems.length > 0 || !query) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });
            });
        }
        
        // Initialize the documentation
        document.addEventListener('DOMContentLoaded', function() {
            generateNavigation();
            generateApiList();
            setupSearch();
            
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>'''
    
    # Write the enhanced HTML file
    with open('travel-pilot-api-docs-detailed.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print('✅ Enhanced API documentation with detailed JSON examples generated!')
    print('📄 File: travel-pilot-api-docs-detailed.html')
    print(f'📊 Total APIs: {len(api_data)} with complete JSON specifications')

if __name__ == "__main__":
    create_enhanced_api_docs()