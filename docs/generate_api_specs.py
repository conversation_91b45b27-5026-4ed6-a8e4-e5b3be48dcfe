import json
import re
from datetime import datetime, timed<PERSON><PERSON>

def create_api_json_specs(api_data):
    """Create detailed JSON specifications for each API"""
    
    # Common response structure
    def success_response(data):
        return {
            "code": 200,
            "message": "success",
            "data": data,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def error_response(code, message):
        return {
            "code": code,
            "message": message,
            "data": None,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    # API specifications with detailed JSON examples
    api_specs = {}
    
    for api in api_data:
        api_id = api['接口编号']
        api_name = api['接口名称']
        input_params = api['入参']
        output_params = api['必要出参']
        description = api.get('出参说明', '')
        
        # Generate request/response based on API functionality
        spec = generate_api_spec(api_id, api_name, input_params, output_params, description)
        api_specs[f"api_{api_id}"] = spec
    
    return api_specs

def generate_api_spec(api_id, api_name, input_params, output_params, description):
    """Generate specific API specification based on parameters"""
    
    # Base URL and endpoint
    endpoint = f"/api/v1/{generate_endpoint_name(api_name)}"
    
    # Parse input parameters
    request_body = parse_input_params(input_params, api_name)
    
    # Parse output parameters  
    response_data = parse_output_params(output_params, api_name, description)
    
    # Generate examples
    request_example = generate_request_example(request_body, api_name)
    response_example = {
        "code": 200,
        "message": "success", 
        "data": generate_response_example(response_data, api_name),
        "timestamp": "2025-07-31 14:30:25"
    }
    
    return {
        "api_id": api_id,
        "api_name": api_name,
        "method": "POST",
        "endpoint": endpoint,
        "description": f"{api_name}接口，用于{get_api_purpose(api_name)}",
        "request": {
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer {access_token}",
                "X-Enterprise-ID": "{enterprise_id}"
            },
            "body_schema": request_body,
            "example": request_example
        },
        "response": {
            "success": {
                "schema": {
                    "code": "int - 状态码",
                    "message": "string - 响应消息", 
                    "data": response_data,
                    "timestamp": "string - 响应时间"
                },
                "example": response_example
            },
            "error": {
                "example": {
                    "code": 400,
                    "message": "参数错误",
                    "data": None,
                    "timestamp": "2025-07-31 14:30:25"
                }
            }
        }
    }

def generate_endpoint_name(api_name):
    """Generate REST endpoint name from API name"""
    mapping = {
        "员工信息同步": "employee/sync",
        "获取员工代订类别": "employee/booking-types", 
        "获取出行人详细信息": "traveler/details",
        "是否在代订范围": "booking/permission-check",
        "获取出行人卡信息": "traveler/cards",
        "获取员工喜好": "employee/preferences",
        "获取员工是否高消费": "employee/high-consumption",
        "获取员工各品类是否管控差标": "employee/category-controls",
        "获取企业POI": "enterprise/poi",
        "获取企业参照人信息": "enterprise/reference-person",
        "获取企业差旅政策": "enterprise/travel-policy",
        "获取差旅政策详情": "travel-policy/details",
        "获取政策适用人员范围": "policy/applicable-scope",
        "差旅政策匹配": "policy/match",
        "获取出行要求": "travel/requirements",
        "获取可用出行方案": "travel/available-plans",
        "创建差旅单": "travel-order/create",
        "差旅单状态更新": "travel-order/status-update",
        "获取差旅单详情": "travel-order/details",
        "差旅单取消": "travel-order/cancel"
    }
    
    for key, value in mapping.items():
        if key in api_name:
            return value
            
    # Default fallback
    return api_name.lower().replace("获取", "get/").replace("创建", "create/").replace("更新", "update/")

def parse_input_params(input_params, api_name):
    """Parse input parameters into JSON schema"""
    if not input_params or input_params == "—":
        return {}
    
    schema = {}
    
    # Common parameter patterns
    if "员工ID" in input_params:
        schema["employee_id"] = "string - 员工唯一标识"
    if "企业ID" in input_params:
        schema["enterprise_id"] = "string - 企业唯一标识"  
    if "出行人" in input_params:
        if "姓名" in input_params:
            schema["traveler_name"] = "string - 出行人姓名"
        if "员工ID" in input_params:
            schema["traveler_employee_ids"] = "array<string> - 出行人员工ID列表"
    if "单点登录" in input_params:
        schema["sso_token"] = "string - 单点登录令牌"
    if "差旅单" in input_params:
        schema["order_id"] = "string - 差旅单ID"
    if "政策" in input_params:
        schema["policy_id"] = "string - 政策ID"
    if "出发地" in input_params:
        schema["departure"] = "string - 出发地"
    if "目的地" in input_params:
        schema["destination"] = "string - 目的地"
    if "出发时间" in input_params:
        schema["departure_time"] = "string - 出发时间 (YYYY-MM-DD HH:mm:ss)"
    if "返程时间" in input_params:
        schema["return_time"] = "string - 返程时间 (YYYY-MM-DD HH:mm:ss)"
        
    return schema

def parse_output_params(output_params, api_name, description):
    """Parse output parameters into JSON schema"""
    if not output_params or output_params == "—":
        return {}
        
    schema = {}
    
    # Employee related outputs
    if "员工ID" in output_params:
        schema["employee_id"] = "string - 员工唯一标识"
    if "企业ID" in output_params:
        schema["enterprise_id"] = "string - 企业唯一标识"
    if "员工姓名" in output_params or "姓名" in output_params:
        schema["employee_name"] = "string - 员工姓名"
    if "是否极速预订" in output_params:
        schema["is_express_booking"] = "boolean - 是否极速预订员工"
    if "组织架构" in output_params:
        schema["organization"] = "object - 组织架构信息"
    if "VIP等级" in output_params:
        schema["vip_level"] = "string - VIP等级 (NONE/VIP1/VIP2/VIP3)"
    if "联系方式" in output_params:
        schema["contact"] = "object - 联系方式信息"
        
    # Card related outputs
    if "卡类型" in output_params:
        schema["card_type"] = "string - 卡片类型"
    if "卡号" in output_params:
        schema["card_number"] = "string - 卡号"
    if "所属机构" in output_params:
        schema["issuing_institution"] = "string - 发卡机构"
        
    # Policy related outputs
    if "政策" in output_params:
        schema["policies"] = "array<object> - 政策列表"
    if "差标" in output_params:
        schema["expense_standards"] = "object - 差旅标准"
        
    # Location related outputs  
    if "POI" in output_params or "地址" in output_params:
        schema["poi_list"] = "array<object> - POI地址列表"
    if "经纬度" in output_params:
        schema["coordinates"] = "object - 坐标信息"
        
    # Booking related outputs
    if "代订类别" in output_params:
        schema["booking_types"] = "array<string> - 代订类别列表"
    if "可订范围" in output_params:
        schema["booking_permission"] = "boolean - 是否在可订范围"
        
    return schema

def generate_request_example(schema, api_name):
    """Generate realistic request example"""
    example = {}
    
    for key, desc in schema.items():
        if key == "employee_id":
            example[key] = "EMP001"
        elif key == "enterprise_id":
            example[key] = "ENT001"
        elif key == "traveler_name":
            example[key] = "张三"
        elif key == "traveler_employee_ids":
            example[key] = ["EMP001", "EMP002"]
        elif key == "sso_token":
            example[key] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        elif key == "order_id":
            example[key] = "TO2025073100001"
        elif key == "policy_id":
            example[key] = "POL001"
        elif key == "departure":
            example[key] = "北京"
        elif key == "destination":
            example[key] = "上海"
        elif key == "departure_time":
            example[key] = "2025-08-15 09:00:00"
        elif key == "return_time":
            example[key] = "2025-08-17 18:00:00"
        else:
            example[key] = "sample_value"
            
    return example

def generate_response_example(schema, api_name):
    """Generate realistic response example"""
    example = {}
    
    for key, desc in schema.items():
        if key == "employee_id":
            example[key] = "EMP001"
        elif key == "enterprise_id":
            example[key] = "ENT001"
        elif key == "employee_name":
            example[key] = "张三"
        elif key == "is_express_booking":
            example[key] = True
        elif key == "vip_level":
            example[key] = "VIP1"
        elif key == "organization":
            example[key] = {
                "department": "技术部",
                "team": "后端开发组",
                "level": "高级工程师"
            }
        elif key == "contact":
            example[key] = {
                "phone": "13800138000",
                "email": "<EMAIL>"
            }
        elif key == "card_type":
            example[key] = "信用卡"
        elif key == "card_number":
            example[key] = "****1234"
        elif key == "issuing_institution":
            example[key] = "招商银行"
        elif key == "booking_types":
            example[key] = ["本人", "同事-所有员工"]
        elif key == "booking_permission":
            example[key] = True
        elif key == "poi_list":
            example[key] = [
                {
                    "name": "公司总部",
                    "address": "北京市朝阳区xxx路xxx号",
                    "coordinates": {
                        "longitude": 116.397128,
                        "latitude": 39.916527
                    }
                }
            ]
        elif key == "policies":
            example[key] = [
                {
                    "policy_id": "POL001",
                    "policy_name": "标准差旅政策",
                    "effective_date": "2025-01-01"
                }
            ]
        else:
            example[key] = "sample_value"
            
    return example

def get_api_purpose(api_name):
    """Get API purpose description"""
    purposes = {
        "员工信息同步": "同步和获取员工基本信息",
        "获取员工代订类别": "获取员工可代订的人员类别范围",
        "获取出行人详细信息": "获取指定出行人的详细个人信息",
        "获取出行人卡信息": "获取出行人的支付卡片信息",
        "获取员工喜好": "获取员工的出行偏好设置",
        "创建差旅单": "创建新的差旅申请单",
        "差旅单状态更新": "更新差旅单的处理状态",
        "获取差旅单详情": "获取指定差旅单的详细信息",
        "政策匹配": "匹配适用的差旅政策",
        "获取企业POI": "获取企业相关的地理位置信息"
    }
    
    for key, purpose in purposes.items():
        if key in api_name:
            return purpose
            
    return "相关业务处理"

# Load existing API data
with open('api_data.json', 'r', encoding='utf-8') as f:
    api_data = json.load(f)

# Generate comprehensive API specifications
api_specs = create_api_json_specs(api_data)

# Save to file
with open('api_json_specs.json', 'w', encoding='utf-8') as f:
    json.dump(api_specs, f, ensure_ascii=False, indent=2)

print(f"✅ Generated detailed JSON specifications for {len(api_specs)} APIs")
print("📄 File: api_json_specs.json")