import json
import re
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_detailed_api_specs(api_data):
    """Create detailed API specifications with comprehensive field definitions"""
    
    api_specs = {}
    
    for api in api_data:
        api_id = api['接口编号']
        api_name = api['接口名称']
        input_params = api['入参']
        output_params = api['必要出参']
        description = api.get('出参说明', '')
        
        # Generate detailed specification with nested field definitions
        spec = generate_detailed_api_spec(api_id, api_name, input_params, output_params, description)
        api_specs[f"api_{api_id}"] = spec
    
    return api_specs

def generate_detailed_api_spec(api_id, api_name, input_params, output_params, description):
    """Generate detailed API specification with comprehensive field definitions"""
    
    endpoint = f"/api/v1/{generate_endpoint_name(api_name)}"
    
    # Parse and create detailed request/response schemas
    request_schema = create_detailed_request_schema(input_params, api_name)
    response_schema = create_detailed_response_schema(output_params, api_name, description)
    
    # Generate comprehensive examples
    request_example = generate_detailed_request_example(request_schema, api_name)
    response_example = generate_detailed_response_example(response_schema, api_name)
    
    return {
        "api_id": api_id,
        "api_name": api_name,
        "method": "POST",
        "endpoint": endpoint,
        "description": f"{api_name}接口，用于{get_api_purpose(api_name)}",
        "request": {
            "headers": {
                "Content-Type": {
                    "type": "string",
                    "required": True,
                    "description": "请求内容类型",
                    "example": "application/json"
                },
                "Authorization": {
                    "type": "string", 
                    "required": True,
                    "description": "Bearer认证令牌",
                    "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                },
                "X-Enterprise-ID": {
                    "type": "string",
                    "required": True, 
                    "description": "企业唯一标识",
                    "example": "ENT001"
                }
            },
            "body": request_schema,
            "example": request_example
        },
        "response": {
            "success": {
                "schema": {
                    "code": {
                        "type": "integer",
                        "description": "响应状态码",
                        "example": 200,
                        "enum": [200]
                    },
                    "message": {
                        "type": "string",
                        "description": "响应消息",
                        "example": "success"
                    },
                    "data": response_schema,
                    "timestamp": {
                        "type": "string",
                        "format": "datetime",
                        "description": "响应时间戳",
                        "example": "2025-07-31 14:30:25"
                    }
                },
                "example": {
                    "code": 200,
                    "message": "success",
                    "data": response_example,
                    "timestamp": "2025-07-31 14:30:25"
                }
            },
            "error": {
                "schema": {
                    "code": {
                        "type": "integer",
                        "description": "错误状态码",
                        "enum": [400, 401, 403, 404, 500]
                    },
                    "message": {
                        "type": "string", 
                        "description": "错误消息"
                    },
                    "data": {
                        "type": "null",
                        "description": "错误时数据为空"
                    },
                    "timestamp": {
                        "type": "string",
                        "format": "datetime", 
                        "description": "响应时间戳"
                    }
                },
                "examples": {
                    "400": {
                        "code": 400,
                        "message": "请求参数错误",
                        "data": None,
                        "timestamp": "2025-07-31 14:30:25"
                    },
                    "401": {
                        "code": 401,
                        "message": "未授权访问",
                        "data": None,
                        "timestamp": "2025-07-31 14:30:25"
                    },
                    "500": {
                        "code": 500,
                        "message": "服务器内部错误",
                        "data": None,
                        "timestamp": "2025-07-31 14:30:25"
                    }
                }
            }
        }
    }

def create_detailed_request_schema(input_params, api_name):
    """Create detailed request schema with comprehensive field definitions"""
    if not input_params or input_params == "—":
        return {}
    
    schema = {}
    
    # Employee related parameters
    if "员工ID" in input_params:
        schema["employee_id"] = {
            "type": "string",
            "required": True,
            "description": "员工唯一标识符",
            "example": "EMP001",
            "pattern": "^EMP[0-9]{3,}$",
            "minLength": 6,
            "maxLength": 20
        }
    
    if "企业ID" in input_params:
        schema["enterprise_id"] = {
            "type": "string", 
            "required": True,
            "description": "企业唯一标识符",
            "example": "ENT001",
            "pattern": "^ENT[0-9]{3,}$"
        }
    
    if "出行人" in input_params and "姓名" in input_params:
        schema["traveler_name"] = {
            "type": "string",
            "required": True,
            "description": "出行人员姓名",
            "example": "张三",
            "minLength": 2,
            "maxLength": 50
        }
    
    if "出行人" in input_params and "员工ID" in input_params:
        schema["traveler_employee_ids"] = {
            "type": "array",
            "items": {
                "type": "string",
                "pattern": "^EMP[0-9]{3,}$"
            },
            "required": False,
            "description": "出行人员工ID列表，支持多个出行人",
            "example": ["EMP001", "EMP002"],
            "minItems": 1,
            "maxItems": 10
        }
    
    if "单点登录" in input_params:
        schema["sso_token"] = {
            "type": "string",
            "required": True,
            "description": "单点登录认证令牌",
            "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
            "minLength": 100
        }
    
    # Travel related parameters
    if "出发地" in input_params:
        schema["departure"] = {
            "type": "string",
            "required": True,
            "description": "出发地城市或地点",
            "example": "北京",
            "minLength": 2,
            "maxLength": 100
        }
    
    if "目的地" in input_params:
        schema["destination"] = {
            "type": "string",
            "required": True, 
            "description": "目的地城市或地点",
            "example": "上海",
            "minLength": 2,
            "maxLength": 100
        }
    
    if "出发时间" in input_params:
        schema["departure_time"] = {
            "type": "string",
            "format": "datetime",
            "required": True,
            "description": "计划出发时间",
            "example": "2025-08-15 09:00:00",
            "pattern": "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$"
        }
    
    if "返程时间" in input_params or "回程时间" in input_params:
        schema["return_time"] = {
            "type": "string",
            "format": "datetime",
            "required": False,
            "description": "计划返程时间（单程时可为空）",
            "example": "2025-08-17 18:00:00",
            "pattern": "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$"
        }
    
    # Order related parameters
    if "差旅单" in input_params or "订单" in input_params:
        schema["order_id"] = {
            "type": "string",
            "required": True,
            "description": "差旅单唯一标识",
            "example": "TO2025073100001",
            "pattern": "^TO\\d{13}$"
        }
    
    if "政策" in input_params:
        schema["policy_id"] = {
            "type": "string",
            "required": False,
            "description": "差旅政策ID",
            "example": "POL001",
            "pattern": "^POL[0-9]{3,}$"
        }
    
    # Add page and size parameters for list APIs
    if "获取" in api_name and ("列表" in api_name or "信息" in api_name):
        schema["page"] = {
            "type": "integer",
            "required": False,
            "description": "页码，从1开始",
            "example": 1,
            "minimum": 1,
            "default": 1
        }
        schema["page_size"] = {
            "type": "integer", 
            "required": False,
            "description": "每页数量",
            "example": 20,
            "minimum": 1,
            "maximum": 100,
            "default": 20
        }
    
    return schema

def create_detailed_response_schema(output_params, api_name, description):
    """Create detailed response schema with comprehensive field definitions"""
    if not output_params or output_params == "—":
        return {}
    
    schema = {}
    
    # Employee related fields
    if "员工ID" in output_params:
        schema["employee_id"] = {
            "type": "string",
            "description": "员工唯一标识符",
            "example": "EMP001",
            "pattern": "^EMP[0-9]{3,}$"
        }
    
    if "企业ID" in output_params:
        schema["enterprise_id"] = {
            "type": "string",
            "description": "企业唯一标识符", 
            "example": "ENT001",
            "pattern": "^ENT[0-9]{3,}$"
        }
    
    if "员工姓名" in output_params or "姓名" in output_params:
        schema["employee_name"] = {
            "type": "string",
            "description": "员工姓名",
            "example": "张三",
            "minLength": 2,
            "maxLength": 50
        }
    
    if "是否极速预订" in output_params:
        schema["is_express_booking"] = {
            "type": "boolean",
            "description": "是否为极速预订员工",
            "example": True
        }
    
    if "组织架构" in output_params:
        schema["organization"] = {
            "type": "object",
            "description": "员工组织架构信息",
            "properties": {
                "company_id": {
                    "type": "string",
                    "description": "公司ID",
                    "example": "COMP001"
                },
                "company_name": {
                    "type": "string", 
                    "description": "公司名称",
                    "example": "科技有限公司"
                },
                "department_id": {
                    "type": "string",
                    "description": "部门ID", 
                    "example": "DEPT001"
                },
                "department_name": {
                    "type": "string",
                    "description": "部门名称",
                    "example": "技术部"
                },
                "team_id": {
                    "type": "string",
                    "description": "团队ID",
                    "example": "TEAM001"
                },
                "team_name": {
                    "type": "string",
                    "description": "团队名称", 
                    "example": "后端开发组"
                },
                "job_title": {
                    "type": "string",
                    "description": "职位名称",
                    "example": "高级工程师"
                },
                "job_level": {
                    "type": "string",
                    "description": "职级",
                    "example": "P7"
                },
                "manager_id": {
                    "type": "string",
                    "description": "直属领导员工ID",
                    "example": "EMP100"
                },
                "cost_center": {
                    "type": "string",
                    "description": "成本中心",
                    "example": "CC001"
                }
            },
            "example": {
                "company_id": "COMP001",
                "company_name": "科技有限公司",
                "department_id": "DEPT001", 
                "department_name": "技术部",
                "team_id": "TEAM001",
                "team_name": "后端开发组",
                "job_title": "高级工程师",
                "job_level": "P7",
                "manager_id": "EMP100",
                "cost_center": "CC001"
            }
        }
    
    if "VIP等级" in output_params:
        schema["vip_level"] = {
            "type": "string",
            "description": "VIP等级",
            "enum": ["NONE", "VIP1", "VIP2", "VIP3"],
            "example": "VIP1"
        }
    
    if "联系方式" in output_params:
        schema["contact"] = {
            "type": "object",
            "description": "联系方式信息",
            "properties": {
                "mobile": {
                    "type": "string",
                    "description": "手机号码",
                    "example": "13800138000",
                    "pattern": "^1[3-9]\\d{9}$"
                },
                "email": {
                    "type": "string",
                    "format": "email",
                    "description": "邮箱地址",
                    "example": "<EMAIL>"
                },
                "work_phone": {
                    "type": "string",
                    "description": "工作电话",
                    "example": "010-12345678"
                },
                "emergency_contact": {
                    "type": "object",
                    "description": "紧急联系人",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "紧急联系人姓名",
                            "example": "李四"
                        },
                        "phone": {
                            "type": "string", 
                            "description": "紧急联系人电话",
                            "example": "13900139000"
                        },
                        "relationship": {
                            "type": "string",
                            "description": "关系",
                            "example": "配偶"
                        }
                    }
                }
            },
            "example": {
                "mobile": "13800138000",
                "email": "<EMAIL>", 
                "work_phone": "010-12345678",
                "emergency_contact": {
                    "name": "李四",
                    "phone": "13900139000",
                    "relationship": "配偶"
                }
            }
        }
    
    # Document related fields
    if "证件" in output_params:
        schema["documents"] = {
            "type": "array",
            "description": "证件信息列表",
            "items": {
                "type": "object",
                "properties": {
                    "document_type": {
                        "type": "string",
                        "description": "证件类型",
                        "enum": ["身份证", "护照", "港澳通行证", "台湾通行证", "驾驶证"],
                        "example": "身份证"
                    },
                    "document_number": {
                        "type": "string",
                        "description": "证件号码",
                        "example": "110101199001011234"
                    },
                    "expiry_date": {
                        "type": "string",
                        "format": "date",
                        "description": "有效期至",
                        "example": "2030-12-31"
                    },
                    "issuing_authority": {
                        "type": "string",
                        "description": "发证机关",
                        "example": "北京市公安局"
                    }
                }
            },
            "example": [
                {
                    "document_type": "身份证",
                    "document_number": "110101199001011234", 
                    "expiry_date": "2030-12-31",
                    "issuing_authority": "北京市公安局"
                }
            ]
        }
    
    # Card related fields
    if "卡类型" in output_params or "卡号" in output_params:
        schema["cards"] = {
            "type": "array",
            "description": "支付卡片信息列表",
            "items": {
                "type": "object",
                "properties": {
                    "card_id": {
                        "type": "string",
                        "description": "卡片ID",
                        "example": "CARD001"
                    },
                    "card_type": {
                        "type": "string",
                        "description": "卡片类型",
                        "enum": ["信用卡", "借记卡", "企业卡", "预付卡"],
                        "example": "信用卡"
                    },
                    "card_number": {
                        "type": "string",
                        "description": "卡号（脱敏显示）",
                        "example": "****1234",
                        "pattern": "\\*{4}\\d{4}"
                    },
                    "card_holder": {
                        "type": "string",
                        "description": "持卡人姓名",
                        "example": "张三"
                    },
                    "issuing_bank": {
                        "type": "string",
                        "description": "发卡银行",
                        "example": "招商银行"
                    },
                    "is_default": {
                        "type": "boolean",
                        "description": "是否为默认卡片",
                        "example": True
                    },
                    "is_corporate": {
                        "type": "boolean",
                        "description": "是否为企业卡",
                        "example": False
                    },
                    "status": {
                        "type": "string",
                        "description": "卡片状态",
                        "enum": ["active", "inactive", "expired"],
                        "example": "active"
                    }
                }
            },
            "example": [
                {
                    "card_id": "CARD001",
                    "card_type": "信用卡",
                    "card_number": "****1234",
                    "card_holder": "张三",
                    "issuing_bank": "招商银行",
                    "is_default": True,
                    "is_corporate": False,
                    "status": "active"
                }
            ]
        }
    
    # Booking related fields
    if "代订类别" in output_params:
        schema["booking_types"] = {
            "type": "array",
            "description": "可代订人员类别列表",
            "items": {
                "type": "string",
                "enum": ["本人", "同事-所有员工", "同事-自定义范围", "客人"]
            },
            "example": ["本人", "同事-所有员工"],
            "minItems": 1
        }
    
    if "可订范围" in output_params:
        schema["booking_permissions"] = {
            "type": "array",
            "description": "代订权限详情",
            "items": {
                "type": "object",
                "properties": {
                    "target_employee_id": {
                        "type": "string",
                        "description": "目标员工ID",
                        "example": "EMP002"
                    },
                    "target_employee_name": {
                        "type": "string",
                        "description": "目标员工姓名",
                        "example": "李四"
                    },
                    "can_book": {
                        "type": "boolean",
                        "description": "是否可以代订",
                        "example": True
                    },
                    "permission_reason": {
                        "type": "string",
                        "description": "权限原因",
                        "example": "同部门员工"
                    }
                }
            },
            "example": [
                {
                    "target_employee_id": "EMP002",
                    "target_employee_name": "李四",
                    "can_book": True,
                    "permission_reason": "同部门员工"
                }
            ]
        }
    
    # POI related fields
    if "POI" in output_params or "地址" in output_params:
        schema["poi_list"] = {
            "type": "array",
            "description": "POI地址信息列表",
            "items": {
                "type": "object",
                "properties": {
                    "poi_id": {
                        "type": "string",
                        "description": "POI唯一标识",
                        "example": "POI001"
                    },
                    "poi_name": {
                        "type": "string",
                        "description": "POI名称",
                        "example": "公司总部"
                    },
                    "poi_type": {
                        "type": "string",
                        "description": "POI类型",
                        "enum": ["office", "hotel", "airport", "station", "other"],
                        "example": "office"
                    },
                    "address": {
                        "type": "string",
                        "description": "详细地址",
                        "example": "北京市朝阳区xxx路xxx号"
                    },
                    "city": {
                        "type": "string",
                        "description": "所在城市",
                        "example": "北京"
                    },
                    "coordinates": {
                        "type": "object",
                        "description": "坐标信息",
                        "properties": {
                            "longitude": {
                                "type": "number",
                                "description": "经度",
                                "example": 116.397128
                            },
                            "latitude": {
                                "type": "number",
                                "description": "纬度",
                                "example": 39.916527
                            },
                            "coordinate_system": {
                                "type": "string",
                                "description": "坐标系统",
                                "enum": ["WGS84", "GCJ02", "BD09"],
                                "example": "GCJ02"
                            }
                        }
                    }
                }
            },
            "example": [
                {
                    "poi_id": "POI001",
                    "poi_name": "公司总部",
                    "poi_type": "office",
                    "address": "北京市朝阳区xxx路xxx号",
                    "city": "北京",
                    "coordinates": {
                        "longitude": 116.397128,
                        "latitude": 39.916527,
                        "coordinate_system": "GCJ02"
                    }
                }
            ]
        }
    
    # Policy related fields
    if "政策" in output_params:
        schema["policies"] = {
            "type": "array",
            "description": "差旅政策列表",
            "items": {
                "type": "object",
                "properties": {
                    "policy_id": {
                        "type": "string",
                        "description": "政策ID",
                        "example": "POL001"
                    },
                    "policy_name": {
                        "type": "string",
                        "description": "政策名称",
                        "example": "标准差旅政策"
                    },
                    "policy_type": {
                        "type": "string",
                        "description": "政策类型",
                        "enum": ["flight", "hotel", "train", "taxi", "meal"],
                        "example": "flight"
                    },
                    "effective_date": {
                        "type": "string",
                        "format": "date",
                        "description": "生效日期",
                        "example": "2025-01-01"
                    },
                    "expiry_date": {
                        "type": "string",
                        "format": "date",
                        "description": "失效日期",
                        "example": "2025-12-31"
                    },
                    "rules": {
                        "type": "object",
                        "description": "政策规则",
                        "properties": {
                            "max_amount": {
                                "type": "number",
                                "description": "最大金额限制",
                                "example": 5000.00
                            },
                            "booking_advance_days": {
                                "type": "integer",
                                "description": "提前预订天数要求",
                                "example": 3
                            },
                            "approval_required": {
                                "type": "boolean",
                                "description": "是否需要审批",
                                "example": True
                            }
                        }
                    }
                }
            },
            "example": [
                {
                    "policy_id": "POL001",
                    "policy_name": "标准差旅政策",
                    "policy_type": "flight",
                    "effective_date": "2025-01-01", 
                    "expiry_date": "2025-12-31",
                    "rules": {
                        "max_amount": 5000.00,
                        "booking_advance_days": 3,
                        "approval_required": True
                    }
                }
            ]
        }
    
    # Travel order related fields
    if "差旅单" in output_params or "订单" in api_name:
        schema["order_id"] = {
            "type": "string",
            "description": "差旅单唯一标识",
            "example": "TO2025073100001",
            "pattern": "^TO\\d{13}$"
        }
        schema["order_status"] = {
            "type": "string",
            "description": "差旅单状态",
            "enum": ["draft", "submitted", "approved", "rejected", "cancelled", "completed"],
            "example": "submitted"
        }
        schema["create_time"] = {
            "type": "string",
            "format": "datetime",
            "description": "创建时间",
            "example": "2025-07-31 14:30:25"
        }
        schema["update_time"] = {
            "type": "string",
            "format": "datetime", 
            "description": "更新时间",
            "example": "2025-07-31 14:30:25"
        }
    
    # Add pagination info for list APIs
    if "获取" in api_name and ("列表" in api_name or "信息" in api_name):
        schema["pagination"] = {
            "type": "object",
            "description": "分页信息",
            "properties": {
                "current_page": {
                    "type": "integer",
                    "description": "当前页码",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "description": "每页数量",
                    "example": 20
                },
                "total_count": {
                    "type": "integer",
                    "description": "总记录数",
                    "example": 150
                },
                "total_pages": {
                    "type": "integer",
                    "description": "总页数",
                    "example": 8
                },
                "has_next": {
                    "type": "boolean",
                    "description": "是否有下一页",
                    "example": True
                },
                "has_prev": {
                    "type": "boolean",
                    "description": "是否有上一页",
                    "example": False
                }
            },
            "example": {
                "current_page": 1,
                "page_size": 20,
                "total_count": 150,
                "total_pages": 8,
                "has_next": True,
                "has_prev": False
            }
        }
    
    return schema

def generate_detailed_request_example(schema, api_name):
    """Generate realistic request example from detailed schema"""
    example = {}
    
    for key, field_def in schema.items():
        if isinstance(field_def, dict) and "example" in field_def:
            example[key] = field_def["example"]
        elif key == "page":
            example[key] = 1
        elif key == "page_size": 
            example[key] = 20
    
    return example

def generate_detailed_response_example(schema, api_name):
    """Generate realistic response example from detailed schema"""
    example = {}
    
    for key, field_def in schema.items():
        if isinstance(field_def, dict) and "example" in field_def:
            example[key] = field_def["example"]
    
    return example

def generate_endpoint_name(api_name):
    """Generate REST endpoint name from API name"""
    mapping = {
        "员工信息同步": "employee/sync",
        "获取员工代订类别": "employee/booking-types",
        "获取出行人详细信息": "traveler/details", 
        "是否在代订范围": "booking/permission-check",
        "获取出行人卡信息": "traveler/cards",
        "获取员工喜好": "employee/preferences",
        "获取员工是否高消费": "employee/high-consumption",
        "获取员工各品类是否管控差标": "employee/category-controls",
        "获取企业POI": "enterprise/poi",
        "获取企业参照人信息": "enterprise/reference-person",
        "获取企业差旅政策": "enterprise/travel-policy",
        "获取差旅政策详情": "travel-policy/details",
        "获取政策适用人员范围": "policy/applicable-scope",
        "差旅政策匹配": "policy/match",
        "获取出行要求": "travel/requirements",
        "获取可用出行方案": "travel/available-plans",
        "创建差旅单": "travel-order/create",
        "差旅单状态更新": "travel-order/status-update",
        "获取差旅单详情": "travel-order/details",
        "差旅单取消": "travel-order/cancel"
    }
    
    for key, value in mapping.items():
        if key in api_name:
            return value
    
    # Default fallback
    clean_name = api_name.replace("获取", "").replace("创建", "create/").replace("更新", "update/")
    return clean_name.lower()

def get_api_purpose(api_name):
    """Get API purpose description"""
    purposes = {
        "员工信息同步": "同步和获取员工基本信息",
        "获取员工代订类别": "获取员工可代订的人员类别范围",
        "获取出行人详细信息": "获取指定出行人的详细个人信息",
        "获取出行人卡信息": "获取出行人的支付卡片信息",
        "获取员工喜好": "获取员工的出行偏好设置",
        "创建差旅单": "创建新的差旅申请单",
        "差旅单状态更新": "更新差旅单的处理状态",
        "获取差旅单详情": "获取指定差旅单的详细信息",
        "政策匹配": "匹配适用的差旅政策",
        "获取企业POI": "获取企业相关的地理位置信息"
    }
    
    for key, purpose in purposes.items():
        if key in api_name:
            return purpose
    
    return "相关业务处理"

# Load existing API data
with open('api_data.json', 'r', encoding='utf-8') as f:
    api_data = json.load(f)

# Generate detailed API specifications
detailed_specs = create_detailed_api_specs(api_data)

# Save to file
with open('api_detailed_specs.json', 'w', encoding='utf-8') as f:
    json.dump(detailed_specs, f, ensure_ascii=False, indent=2)

print(f"✅ Generated detailed field specifications for {len(detailed_specs)} APIs")
print("📄 File: api_detailed_specs.json")
print("🔍 Each API now includes comprehensive field definitions with:")
print("  - Field types and validation rules")
print("  - Nested object structures") 
print("  - Enum values and examples")
print("  - Required/optional indicators")
print("  - Field descriptions and constraints")