import json

# Read the API data
with open('api_data.json', 'r', encoding='utf-8') as f:
    api_data = json.load(f)

# Generate the complete HTML with embedded API data
html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Pilot API 接口文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .sidebar {
            position: fixed;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 250px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .sidebar ul {
            list-style: none;
        }
        
        .sidebar li {
            margin-bottom: 8px;
        }
        
        .sidebar a {
            text-decoration: none;
            color: #666;
            font-size: 14px;
            display: block;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .sidebar a:hover {
            background-color: #f0f2ff;
            color: #667eea;
        }
        
        .content {
            margin-left: 290px;
        }
        
        .common-params {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .api-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .api-section h2 {
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f2ff;
        }
        
        .api-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .api-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .api-header:hover {
            background: #f0f2ff;
        }
        
        .api-title {
            font-weight: bold;
            color: #495057;
            margin-left: 10px;
        }
        
        .api-id {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .api-priority {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .priority-high {
            background: #ff6b6b;
            color: white;
        }
        
        .priority-medium {
            background: #ffd93d;
            color: #333;
        }
        
        .priority-low {
            background: #6bcf7f;
            color: white;
        }
        
        .api-content {
            padding: 20px;
            display: none;
        }
        
        .api-content.active {
            display: block;
        }
        
        .param-section {
            margin-bottom: 20px;
        }
        
        .param-section h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .param-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .param-content ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .param-content li {
            margin-bottom: 5px;
        }
        
        .meta-info {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            font-size: 14px;
            color: #666;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            .content {
                margin-left: 0;
            }
        }
        
        .search-box {
            margin-bottom: 20px;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .toggle-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 18px;
        }
        
        .workflow-count {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Travel Pilot API 接口文档</h1>
            <p>企业差旅管理系统接口规范 - 共 ''' + str(len(api_data)) + ''' 个接口</p>
        </div>
        
        <div class="sidebar">
            <h3>接口导航</h3>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索接口...">
            </div>
            <ul id="apiNavigation"></ul>
        </div>
        
        <div class="content">
            <!-- 公共参数 -->
            <div class="common-params" id="common-params">
                <h2>🔧 公共参数</h2>
                <div class="param-section">
                    <h4>请求头 (Headers)</h4>
                    <div class="param-content">
                        <p><strong>Content-Type:</strong> application/json</p>
                        <p><strong>Authorization:</strong> Bearer {access_token}</p>
                        <p><strong>X-Enterprise-ID:</strong> {企业ID}</p>
                    </div>
                </div>
                
                <div class="param-section">
                    <h4>公共响应参数</h4>
                    <div class="param-content">
                        <p><strong>code:</strong> 响应状态码 (200: 成功, 400: 参数错误, 401: 未授权, 500: 服务器错误)</p>
                        <p><strong>message:</strong> 响应消息</p>
                        <p><strong>data:</strong> 响应数据</p>
                        <p><strong>timestamp:</strong> 响应时间戳</p>
                    </div>
                </div>
            </div>
            
            <!-- 获取访问令牌 -->
            <div class="api-section" id="access-token">
                <h2>🔐 身份认证</h2>
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('auth-token')">
                        <div>
                            <span class="api-id">AUTH</span>
                            <span class="api-title">获取访问令牌 (Access Token)</span>
                        </div>
                        <div>
                            <span class="api-priority priority-high">高优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="auth-token">
                        <div class="param-section">
                            <h4>📝 接口描述</h4>
                            <div class="param-content">
                                <p>通过企业凭证获取API访问令牌，用于后续接口调用的身份验证。</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📤 请求参数</h4>
                            <div class="param-content">
                                <ul>
                                    <li><strong>client_id:</strong> 企业客户端ID</li>
                                    <li><strong>client_secret:</strong> 企业客户端密钥</li>
                                    <li><strong>grant_type:</strong> 授权类型 (固定值: client_credentials)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📥 响应参数</h4>
                            <div class="param-content">
                                <ul>
                                    <li><strong>access_token:</strong> 访问令牌</li>
                                    <li><strong>token_type:</strong> 令牌类型 (Bearer)</li>
                                    <li><strong>expires_in:</strong> 令牌有效期 (秒)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 单点登录 -->
            <div class="api-section" id="sso">
                <h2>👤 单点登录</h2>
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('sso-login')">
                        <div>
                            <span class="api-id">SSO</span>
                            <span class="api-title">员工单点登录认证</span>
                        </div>
                        <div>
                            <span class="api-priority priority-high">高优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="sso-login">
                        <div class="param-section">
                            <h4>📝 接口描述</h4>
                            <div class="param-content">
                                <p>通过企业单点登录系统验证员工身份，获取员工基本信息。</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📤 请求参数</h4>
                            <div class="param-content">
                                <ul>
                                    <li><strong>sso_token:</strong> 单点登录令牌</li>
                                    <li><strong>employee_id:</strong> 员工ID (可选)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📥 响应参数</h4>
                            <div class="param-content">
                                <ul>
                                    <li><strong>employee_id:</strong> 员工ID</li>
                                    <li><strong>enterprise_id:</strong> 企业ID</li>
                                    <li><strong>employee_name:</strong> 员工姓名</li>
                                    <li><strong>is_express_booking:</strong> 是否极速预订员工</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- API接口列表 -->
            <div id="apiList"></div>
        </div>
    </div>

    <script>
        const apiData = ''' + json.dumps(api_data, ensure_ascii=False, indent=8) + ''';
        
        // Group APIs by workflow
        function groupApisByWorkflow() {
            const workflows = {};
            apiData.forEach(api => {
                const workflow = api['所属流程'] || '其他';
                if (!workflows[workflow]) {
                    workflows[workflow] = [];
                }
                workflows[workflow].push(api);
            });
            return workflows;
        }
        
        // Generate navigation
        function generateNavigation() {
            const navigation = document.getElementById('apiNavigation');
            const workflows = groupApisByWorkflow();
            
            // Add common sections
            navigation.innerHTML = `
                <li><a href="#common-params">🔧 公共参数</a></li>
                <li><a href="#access-token">🔐 身份认证</a></li>
                <li><a href="#sso">👤 单点登录</a></li>
                <li style="border-top: 1px solid #eee; margin-top: 10px; padding-top: 10px;"></li>
            `;
            
            Object.keys(workflows).sort().forEach(workflow => {
                const li = document.createElement('li');
                li.innerHTML = `<a href="#workflow-${workflow}">${workflow}<span class="workflow-count">${workflows[workflow].length}</span></a>`;
                navigation.appendChild(li);
            });
        }
        
        // Generate API list
        function generateApiList() {
            const apiList = document.getElementById('apiList');
            const workflows = groupApisByWorkflow();
            
            Object.keys(workflows).sort().forEach(workflow => {
                const section = document.createElement('div');
                section.className = 'api-section';
                section.id = `workflow-${workflow}`;
                
                section.innerHTML = `
                    <h2>📋 ${workflow} (${workflows[workflow].length}个接口)</h2>
                    ${workflows[workflow].map(api => generateApiItem(api)).join('')}
                `;
                
                apiList.appendChild(section);
            });
        }
        
        // Generate individual API item
        function generateApiItem(api) {
            const priorityClass = api['优先级'] === '高' ? 'priority-high' : 
                                api['优先级'] === '中' ? 'priority-medium' : 'priority-low';
            
            const apiId = `api-${api['接口编号']}`;
            
            return `
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('${apiId}')">
                        <div>
                            <span class="api-id">API-${api['接口编号']}</span>
                            <span class="api-title">${api['接口名称']}</span>
                        </div>
                        <div>
                            <span class="api-priority ${priorityClass}">${api['优先级']}优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="${apiId}">
                        <div class="param-section">
                            <h4>📝 接口描述</h4>
                            <div class="param-content">
                                <p>${api['接口名称']} - 用于${api['所属流程']}。</p>
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📤 请求参数</h4>
                            <div class="param-content">
                                ${formatParams(api['入参'])}
                            </div>
                        </div>
                        
                        <div class="param-section">
                            <h4>📥 响应参数</h4>
                            <div class="param-content">
                                ${formatParams(api['必要出参'])}
                            </div>
                        </div>
                        
                        ${api['出参说明'] && api['出参说明'] !== '—' ? `
                        <div class="param-section">
                            <h4>📋 说明</h4>
                            <div class="param-content">
                                <p>${api['出参说明']}</p>
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="meta-info">
                            <div class="meta-item">
                                <span>🎯</span>
                                <span>所属流程: ${api['所属流程']}</span>
                            </div>
                            <div class="meta-item">
                                <span>📅</span>
                                <span>计划交付: ${api['计划交付时间']}</span>
                            </div>
                            <div class="meta-item">
                                <span>⚡</span>
                                <span>优先级: ${api['优先级']}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Format parameters
        function formatParams(params) {
            if (!params || params === '—') {
                return '<p>无特殊参数</p>';
            }
            
            // Split by common delimiters and format
            const items = params.split(/[、，,；;]/).filter(item => item.trim());
            if (items.length > 1) {
                return '<ul>' + items.map(item => `<li><strong>${item.trim()}</strong></li>`).join('') + '</ul>';
            } else {
                return `<p><strong>${params}</strong></p>`;
            }
        }
        
        // Toggle API content visibility
        function toggleApi(apiId) {
            const content = document.getElementById(apiId);
            const isActive = content.classList.contains('active');
            
            // Close all other API contents
            document.querySelectorAll('.api-content.active').forEach(el => {
                el.classList.remove('active');
                el.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▼';
            });
            
            // Toggle current API content
            if (!isActive) {
                content.classList.add('active');
                content.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▲';
            }
        }
        
        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const apiItems = document.querySelectorAll('.api-item');
                
                apiItems.forEach(item => {
                    const title = item.querySelector('.api-title').textContent.toLowerCase();
                    const content = item.textContent.toLowerCase();
                    
                    if (title.includes(query) || content.includes(query)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Show/hide sections based on visible items
                const sections = document.querySelectorAll('.api-section');
                sections.forEach(section => {
                    const visibleItems = section.querySelectorAll('.api-item[style="display: block;"], .api-item:not([style*="display: none"])');
                    if (visibleItems.length > 0 || !query) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });
            });
        }
        
        // Initialize the documentation
        document.addEventListener('DOMContentLoaded', function() {
            generateNavigation();
            generateApiList();
            setupSearch();
            
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>'''

# Write the complete HTML file
with open('travel-pilot-api-docs.html', 'w', encoding='utf-8') as f:
    f.write(html_content)

print('✅ Complete API documentation generated successfully!')
print('📄 File: docs/travel-pilot-api-docs.html')
print(f'📊 Total APIs: {len(api_data)}')

# Count by priority and workflow
high_count = len([api for api in api_data if api['优先级'] == '高'])
medium_count = len([api for api in api_data if api['优先级'] == '中'])
low_count = len([api for api in api_data if api['优先级'] == '低'])

print(f'🔥 高优先级: {high_count} 个')
print(f'🟨 中优先级: {medium_count} 个') 
print(f'🟩 低优先级: {low_count} 个')