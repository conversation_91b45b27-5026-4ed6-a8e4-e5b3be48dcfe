import json
import yaml
from datetime import datetime

def convert_to_openapi():
    """Convert API specifications to OpenAPI 3.0 format"""
    
    # Load existing detailed specifications
    with open('api_data.json', 'r', encoding='utf-8') as f:
        api_data = json.load(f)
    
    with open('api_detailed_specs.json', 'r', encoding='utf-8') as f:
        detailed_specs = json.load(f)
    
    # Create OpenAPI 3.0 specification
    openapi_spec = {
        "openapi": "3.0.3",
        "info": {
            "title": "Travel Pilot API",
            "description": "企业差旅管理系统接口规范",
            "version": "1.0.0",
            "contact": {
                "name": "Travel Pilot API Support",
                "email": "<EMAIL>"
            },
            "license": {
                "name": "MIT",
                "url": "https://opensource.org/licenses/MIT"
            }
        },
        "servers": [
            {
                "url": "https://api.travelpilot.com/v1",
                "description": "生产环境"
            },
            {
                "url": "https://staging-api.travelpilot.com/v1", 
                "description": "测试环境"
            },
            {
                "url": "https://dev-api.travelpilot.com/v1",
                "description": "开发环境"
            }
        ],
        "security": [
            {
                "BearerAuth": []
            }
        ],
        "tags": [
            {"name": "authentication", "description": "身份认证相关接口"},
            {"name": "employee", "description": "员工管理相关接口"},
            {"name": "traveler", "description": "出行人管理相关接口"},
            {"name": "booking", "description": "预订管理相关接口"},
            {"name": "policy", "description": "差旅政策相关接口"},
            {"name": "travel-order", "description": "差旅单管理相关接口"},
            {"name": "enterprise", "description": "企业管理相关接口"}
        ],
        "paths": {},
        "components": {
            "securitySchemes": {
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT"
                }
            },
            "schemas": {},
            "responses": {
                "UnauthorizedError": {
                    "description": "未授权访问",
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/ErrorResponse"
                            }
                        }
                    }
                },
                "BadRequestError": {
                    "description": "请求参数错误",
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/ErrorResponse"
                            }
                        }
                    }
                },
                "InternalServerError": {
                    "description": "服务器内部错误",
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/ErrorResponse"
                            }
                        }
                    }
                }
            },
            "parameters": {
                "EnterpriseIdHeader": {
                    "name": "X-Enterprise-ID",
                    "in": "header",
                    "required": True,
                    "description": "企业唯一标识",
                    "schema": {
                        "type": "string",
                        "pattern": "^ENT[0-9]{3,}$",
                        "example": "ENT001"
                    }
                },
                "PageParam": {
                    "name": "page",
                    "in": "query",
                    "description": "页码，从1开始",
                    "required": False,
                    "schema": {
                        "type": "integer",
                        "minimum": 1,
                        "default": 1,
                        "example": 1
                    }
                },
                "PageSizeParam": {
                    "name": "page_size",
                    "in": "query", 
                    "description": "每页数量",
                    "required": False,
                    "schema": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 100,
                        "default": 20,
                        "example": 20
                    }
                }
            }
        }
    }
    
    # Add common schemas
    add_common_schemas(openapi_spec)
    
    # Convert each API to OpenAPI path
    for api in api_data:
        api_id = api['接口编号']
        spec_key = f"api_{api_id}"
        
        if spec_key in detailed_specs:
            detailed_spec = detailed_specs[spec_key]
            add_api_path(openapi_spec, api, detailed_spec)
    
    return openapi_spec

def add_common_schemas(openapi_spec):
    """Add common schemas to OpenAPI specification"""
    
    openapi_spec["components"]["schemas"].update({
        "SuccessResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "description": "响应状态码",
                    "example": 200
                },
                "message": {
                    "type": "string",
                    "description": "响应消息",
                    "example": "success"
                },
                "data": {
                    "description": "响应数据",
                    "oneOf": [
                        {"type": "object"},
                        {"type": "array"},
                        {"type": "null"}
                    ]
                },
                "timestamp": {
                    "type": "string",
                    "format": "date-time",
                    "description": "响应时间戳",
                    "example": "2025-07-31T14:30:25+08:00"
                }
            },
            "required": ["code", "message", "data", "timestamp"]
        },
        "ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "description": "错误状态码",
                    "enum": [400, 401, 403, 404, 500]
                },
                "message": {
                    "type": "string",
                    "description": "错误消息"
                },
                "data": {
                    "type": "null",
                    "description": "错误时数据为空"
                },
                "timestamp": {
                    "type": "string",
                    "format": "date-time",
                    "description": "响应时间戳"
                }
            },
            "required": ["code", "message", "data", "timestamp"]
        },
        "Pagination": {
            "type": "object",
            "properties": {
                "current_page": {
                    "type": "integer",
                    "description": "当前页码",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "description": "每页数量",
                    "example": 20
                },
                "total_count": {
                    "type": "integer",
                    "description": "总记录数",
                    "example": 150
                },
                "total_pages": {
                    "type": "integer",
                    "description": "总页数",
                    "example": 8
                },
                "has_next": {
                    "type": "boolean",
                    "description": "是否有下一页",
                    "example": True
                },
                "has_prev": {
                    "type": "boolean",
                    "description": "是否有上一页",
                    "example": False
                }
            },
            "required": ["current_page", "page_size", "total_count", "total_pages", "has_next", "has_prev"]
        },
        "Organization": {
            "type": "object",
            "properties": {
                "company_id": {"type": "string", "description": "公司ID", "example": "COMP001"},
                "company_name": {"type": "string", "description": "公司名称", "example": "科技有限公司"},
                "department_id": {"type": "string", "description": "部门ID", "example": "DEPT001"},
                "department_name": {"type": "string", "description": "部门名称", "example": "技术部"},
                "team_id": {"type": "string", "description": "团队ID", "example": "TEAM001"},
                "team_name": {"type": "string", "description": "团队名称", "example": "后端开发组"},
                "job_title": {"type": "string", "description": "职位名称", "example": "高级工程师"},
                "job_level": {"type": "string", "description": "职级", "example": "P7"},
                "manager_id": {"type": "string", "description": "直属领导员工ID", "example": "EMP100"},
                "cost_center": {"type": "string", "description": "成本中心", "example": "CC001"}
            }
        },
        "Contact": {
            "type": "object",
            "properties": {
                "mobile": {
                    "type": "string",
                    "description": "手机号码",
                    "pattern": "^1[3-9]\\d{9}$",
                    "example": "13800138000"
                },
                "email": {
                    "type": "string",
                    "format": "email",
                    "description": "邮箱地址",
                    "example": "<EMAIL>"
                },
                "work_phone": {
                    "type": "string",
                    "description": "工作电话",
                    "example": "010-12345678"
                },
                "emergency_contact": {
                    "$ref": "#/components/schemas/EmergencyContact"
                }
            }
        },
        "EmergencyContact": {
            "type": "object",
            "properties": {
                "name": {"type": "string", "description": "紧急联系人姓名", "example": "李四"},
                "phone": {"type": "string", "description": "紧急联系人电话", "example": "13900139000"},
                "relationship": {"type": "string", "description": "关系", "example": "配偶"}
            }
        },
        "Document": {
            "type": "object",
            "properties": {
                "document_type": {
                    "type": "string",
                    "description": "证件类型",
                    "enum": ["身份证", "护照", "港澳通行证", "台湾通行证", "驾驶证"],
                    "example": "身份证"
                },
                "document_number": {
                    "type": "string",
                    "description": "证件号码",
                    "example": "110101199001011234"
                },
                "expiry_date": {
                    "type": "string",
                    "format": "date",
                    "description": "有效期至",
                    "example": "2030-12-31"
                },
                "issuing_authority": {
                    "type": "string",
                    "description": "发证机关",
                    "example": "北京市公安局"
                }
            }
        },
        "Card": {
            "type": "object",
            "properties": {
                "card_id": {"type": "string", "description": "卡片ID", "example": "CARD001"},
                "card_type": {
                    "type": "string",
                    "description": "卡片类型",
                    "enum": ["信用卡", "借记卡", "企业卡", "预付卡"],
                    "example": "信用卡"
                },
                "card_number": {
                    "type": "string",
                    "description": "卡号（脱敏显示）",
                    "pattern": "\\*{4}\\d{4}",
                    "example": "****1234"
                },
                "card_holder": {"type": "string", "description": "持卡人姓名", "example": "张三"},
                "issuing_bank": {"type": "string", "description": "发卡银行", "example": "招商银行"},
                "is_default": {"type": "boolean", "description": "是否为默认卡片", "example": True},
                "is_corporate": {"type": "boolean", "description": "是否为企业卡", "example": False},
                "status": {
                    "type": "string",
                    "description": "卡片状态",
                    "enum": ["active", "inactive", "expired"],
                    "example": "active"
                }
            }
        },
        "POI": {
            "type": "object",
            "properties": {
                "poi_id": {"type": "string", "description": "POI唯一标识", "example": "POI001"},
                "poi_name": {"type": "string", "description": "POI名称", "example": "公司总部"},
                "poi_type": {
                    "type": "string",
                    "description": "POI类型",
                    "enum": ["office", "hotel", "airport", "station", "other"],
                    "example": "office"
                },
                "address": {"type": "string", "description": "详细地址", "example": "北京市朝阳区xxx路xxx号"},
                "city": {"type": "string", "description": "所在城市", "example": "北京"},
                "coordinates": {
                    "$ref": "#/components/schemas/Coordinates"
                }
            }
        },
        "Coordinates": {
            "type": "object",
            "properties": {
                "longitude": {"type": "number", "description": "经度", "example": 116.397128},
                "latitude": {"type": "number", "description": "纬度", "example": 39.916527},
                "coordinate_system": {
                    "type": "string",
                    "description": "坐标系统",
                    "enum": ["WGS84", "GCJ02", "BD09"],
                    "example": "GCJ02"
                }
            }
        },
        "Policy": {
            "type": "object",
            "properties": {
                "policy_id": {"type": "string", "description": "政策ID", "example": "POL001"},
                "policy_name": {"type": "string", "description": "政策名称", "example": "标准差旅政策"},
                "policy_type": {
                    "type": "string",
                    "description": "政策类型",
                    "enum": ["flight", "hotel", "train", "taxi", "meal"],
                    "example": "flight"
                },
                "effective_date": {"type": "string", "format": "date", "description": "生效日期", "example": "2025-01-01"},
                "expiry_date": {"type": "string", "format": "date", "description": "失效日期", "example": "2025-12-31"},
                "rules": {
                    "$ref": "#/components/schemas/PolicyRules"
                }
            }
        },
        "PolicyRules": {
            "type": "object",
            "properties": {
                "max_amount": {"type": "number", "description": "最大金额限制", "example": 5000.00},
                "booking_advance_days": {"type": "integer", "description": "提前预订天数要求", "example": 3},
                "approval_required": {"type": "boolean", "description": "是否需要审批", "example": True}
            }
        }
    })

def add_api_path(openapi_spec, api, detailed_spec):
    """Add API path to OpenAPI specification"""
    
    path = detailed_spec['endpoint']
    method = detailed_spec['method'].lower()
    
    # Determine tag based on endpoint
    tag = determine_api_tag(path, detailed_spec['api_name'])
    
    # Create path operation
    operation = {
        "tags": [tag],
        "summary": detailed_spec['api_name'],
        "description": detailed_spec['description'],
        "operationId": f"api_{detailed_spec['api_id']}_{method}_{path.replace('/', '_').replace('-', '_')}",
        "parameters": [
            {"$ref": "#/components/parameters/EnterpriseIdHeader"}
        ],
        "responses": {
            "200": {
                "description": "成功响应",
                "content": {
                    "application/json": {
                        "schema": create_success_response_schema(detailed_spec),
                        "example": detailed_spec['response']['success']['example']
                    }
                }
            },
            "400": {"$ref": "#/components/responses/BadRequestError"},
            "401": {"$ref": "#/components/responses/UnauthorizedError"}, 
            "500": {"$ref": "#/components/responses/InternalServerError"}
        }
    }
    
    # Add request body if present
    if detailed_spec['request']['body']:
        operation["requestBody"] = {
            "required": True,
            "content": {
                "application/json": {
                    "schema": convert_field_to_schema(detailed_spec['request']['body']),
                    "example": detailed_spec['request']['example']
                }
            }
        }
    
    # Add query parameters if present
    if any('page' in field for field in detailed_spec['request']['body'].keys()) if detailed_spec['request']['body'] else False:
        operation["parameters"].extend([
            {"$ref": "#/components/parameters/PageParam"},
            {"$ref": "#/components/parameters/PageSizeParam"}
        ])
    
    # Initialize path if not exists
    if path not in openapi_spec["paths"]:
        openapi_spec["paths"][path] = {}
    
    openapi_spec["paths"][path][method] = operation

def determine_api_tag(path, api_name):
    """Determine API tag based on path and name"""
    
    if 'auth' in path or 'token' in path or '登录' in api_name:
        return 'authentication'
    elif 'employee' in path or '员工' in api_name:
        return 'employee'
    elif 'traveler' in path or '出行人' in api_name:
        return 'traveler'
    elif 'booking' in path or '代订' in api_name or '预订' in api_name:
        return 'booking'
    elif 'policy' in path or '政策' in api_name:
        return 'policy'
    elif 'travel-order' in path or '差旅单' in api_name:
        return 'travel-order'
    elif 'enterprise' in path or '企业' in api_name:
        return 'enterprise'
    else:
        return 'employee'  # default

def create_success_response_schema(detailed_spec):
    """Create success response schema"""
    
    data_schema = convert_field_to_schema(detailed_spec['response']['success']['schema']['data'])
    
    return {
        "allOf": [
            {"$ref": "#/components/schemas/SuccessResponse"},
            {
                "properties": {
                    "data": data_schema
                }
            }
        ]
    }

def convert_field_to_schema(field_def):
    """Convert field definition to OpenAPI schema"""
    
    if not field_def:
        return {"type": "object"}
    
    if isinstance(field_def, dict):
        if 'type' in field_def:
            # Single field definition
            schema = {"type": field_def['type']}
            
            if 'description' in field_def:
                schema['description'] = field_def['description']
            if 'example' in field_def:
                schema['example'] = field_def['example']
            if 'enum' in field_def:
                schema['enum'] = field_def['enum']
            if 'pattern' in field_def:
                schema['pattern'] = field_def['pattern']
            if 'format' in field_def:
                schema['format'] = field_def['format']
            if 'minLength' in field_def:
                schema['minLength'] = field_def['minLength']
            if 'maxLength' in field_def:
                schema['maxLength'] = field_def['maxLength']
            if 'minimum' in field_def:
                schema['minimum'] = field_def['minimum']
            if 'maximum' in field_def:
                schema['maximum'] = field_def['maximum']
            
            # Handle object properties
            if field_def['type'] == 'object' and 'properties' in field_def:
                schema['properties'] = {}
                required_fields = []
                
                for prop_name, prop_def in field_def['properties'].items():
                    schema['properties'][prop_name] = convert_field_to_schema(prop_def)
                    if isinstance(prop_def, dict) and prop_def.get('required', False):
                        required_fields.append(prop_name)
                
                if required_fields:
                    schema['required'] = required_fields
            
            # Handle array items
            if field_def['type'] == 'array' and 'items' in field_def:
                schema['items'] = convert_field_to_schema(field_def['items'])
            
            return schema
        else:
            # Object with multiple properties
            schema = {
                "type": "object",
                "properties": {},
                "required": []
            }
            
            for prop_name, prop_def in field_def.items():
                schema['properties'][prop_name] = convert_field_to_schema(prop_def)
                if isinstance(prop_def, dict) and prop_def.get('required', False):
                    schema['required'].append(prop_name)
            
            if not schema['required']:
                del schema['required']
            
            return schema
    
    return {"type": "string", "description": str(field_def)}

def generate_openapi_files():
    """Generate OpenAPI specification in both YAML and JSON formats"""
    
    print("🔄 Converting API specifications to OpenAPI 3.0 format...")
    openapi_spec = convert_to_openapi()
    
    # Generate YAML file
    with open('travel-pilot-openapi.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(openapi_spec, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
    
    # Generate JSON file
    with open('travel-pilot-openapi.json', 'w', encoding='utf-8') as f:
        json.dump(openapi_spec, f, ensure_ascii=False, indent=2)
    
    print("✅ OpenAPI specification generated successfully!")
    print("📄 Files generated:")
    print("  - travel-pilot-openapi.yaml (YAML格式)")
    print("  - travel-pilot-openapi.json (JSON格式)")
    print(f"📊 Total APIs: {len(openapi_spec['paths'])} endpoints")
    print(f"🏷️  Tags: {len(openapi_spec['tags'])} categories")
    print(f"📋 Schemas: {len(openapi_spec['components']['schemas'])} reusable schemas")
    
    return openapi_spec

if __name__ == "__main__":
    generate_openapi_files()