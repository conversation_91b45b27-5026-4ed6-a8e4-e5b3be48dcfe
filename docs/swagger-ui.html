<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>Travel Pilot API 文档</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 0;
      text-align: center;
      margin-bottom: 0;
    }
    .header h1 {
      margin: 0;
      font-size: 2em;
    }
    .header p {
      margin: 10px 0 0 0;
      opacity: 0.9;
    }
    #swagger-ui {
      max-width: 1200px;
      margin: 0 auto;
    }
    .swagger-ui .topbar {
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Travel Pilot API 接口文档</h1>
    <p>企业差旅管理系统 OpenAPI 3.0 规范</p>
  </div>
  
  <div id="swagger-ui"></div>

  <script src="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      // Begin Swagger UI call region
      const ui = SwaggerUIBundle({
        url: './travel-pilot-openapi.yaml',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        validatorUrl: null,
        docExpansion: "list",
        defaultModelsExpandDepth: 2,
        defaultModelExpandDepth: 2,
        displayRequestDuration: true,
        tryItOutEnabled: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        onComplete: function() {
          console.log('Swagger UI loaded successfully');
        },
        onFailure: function(error) {
          console.error('Failed to load Swagger UI:', error);
          document.getElementById('swagger-ui').innerHTML = `
            <div style="padding: 20px; text-align: center; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px;">
              <h3>📋 OpenAPI 文档加载失败</h3>
              <p>请确保以下文件存在于当前目录：</p>
              <ul style="text-align: left; display: inline-block;">
                <li><code>travel-pilot-openapi.yaml</code></li>
                <li><code>travel-pilot-openapi.json</code></li>
              </ul>
              <p>或者使用本地服务器运行此文件（由于浏览器同源策略限制）</p>
              <div style="margin-top: 20px; padding: 15px; background: #d1ecf1; color: #0c5460; border-radius: 5px;">
                <strong>💡 推荐使用方式：</strong><br>
                <code>python3 -m http.server 8000</code><br>
                然后访问：<code>http://localhost:8000/swagger-ui.html</code>
              </div>
            </div>
          `;
        }
      });
      // End Swagger UI call region
    };
  </script>
</body>
</html>