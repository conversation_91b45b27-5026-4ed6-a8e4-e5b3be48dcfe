<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Pilot API 接口文档 - 详细版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .sidebar {
            position: fixed;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 280px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .sidebar ul {
            list-style: none;
        }
        
        .sidebar li {
            margin-bottom: 8px;
        }
        
        .sidebar a {
            text-decoration: none;
            color: #666;
            font-size: 14px;
            display: block;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .sidebar a:hover {
            background-color: #f0f2ff;
            color: #667eea;
        }
        
        .content {
            margin-left: 320px;
        }
        
        .api-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .api-section h2 {
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f2ff;
        }
        
        .api-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .api-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .api-header:hover {
            background: #f0f2ff;
        }
        
        .api-title {
            font-weight: bold;
            color: #495057;
            margin-left: 10px;
        }
        
        .api-id {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .api-method {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .api-priority {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .priority-high {
            background: #ff6b6b;
            color: white;
        }
        
        .priority-medium {
            background: #ffd93d;
            color: #333;
        }
        
        .priority-low {
            background: #6bcf7f;
            color: white;
        }
        
        .api-content {
            padding: 25px;
            display: none;
        }
        
        .api-content.active {
            display: block;
        }
        
        .section-tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .endpoint-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .endpoint-info code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        .json-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .json-header {
            background: #e9ecef;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 14px;
            color: #495057;
        }
        
        .json-content {
            padding: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .param-table th,
        .param-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .param-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .param-table code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .meta-info {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #666;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .search-box {
            margin-bottom: 20px;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .toggle-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 18px;
        }
        
        .workflow-count {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 5px;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #667eea;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-btn:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 1200px) {
            .sidebar {
                display: none;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Travel Pilot API 接口文档</h1>
            <p>企业差旅管理系统接口规范 - 共 52 个接口 (详细版)</p>
        </div>
        
        <div class="sidebar">
            <h3>接口导航</h3>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索接口...">
            </div>
            <ul id="apiNavigation"></ul>
        </div>
        
        <div class="content">
            <div id="apiList"></div>
        </div>
    </div>

    <script>
        const apiData = [
        {
                "接口编号": 1,
                "接口名称": "员工信息同步（预订人）",
                "入参": "员工ID/单点登录",
                "必要出参": "员工ID、企业ID、是否极速预订员工（员工+公司）",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 2,
                "接口名称": "获取员工代订类别",
                "入参": "员工ID",
                "必要出参": "代订类别枚举（本人、同事-所有员工、同事-自定义范围、客人）",
                "出参说明": "枚举值返回，可多值返回，如（本人、同事-所有员工",
                "所属流程": "出行人选择/确认子流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 3,
                "接口名称": "获取出行人详细信息",
                "入参": "出行人姓名、员工ID",
                "必要出参": "件信息列表（类型、号码、有效期）、联系方式（手机、邮箱）、组织架构（全层级）、外部员工ID、VIP等级（否/VIP1/VIP2/VIP3）、base地",
                "出参说明": "获取时返回员工姓名=入参姓名的列表数据",
                "所属流程": "出行人选择/确认子流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 4,
                "接口名称": "是否在代订范围（判断出行人是否在预订人的代订范围）",
                "入参": "预订人-员工ID、出行人-员工ID（支持多值，如：张三，李四）",
                "必要出参": "员工ID、是否在可订范围（键值对返回）",
                "出参说明": "员工ID获取时精准返回，姓名获取时返回员工姓名=入参姓名的列表数据",
                "所属流程": "出行人选择/确认子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 5,
                "接口名称": "获取出行人卡信息",
                "入参": "员工ID",
                "必要出参": "卡类型、所属机构、卡号、是否优选使用",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 6,
                "接口名称": "获取员工喜好",
                "入参": "员工ID",
                "必要出参": "喜好类型、喜好内容",
                "出参说明": "—",
                "所属流程": "出行喜好子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 7,
                "接口名称": "获取员工是否高消费",
                "入参": "员工ID",
                "必要出参": "键值对（员工ID-是/否）",
                "出参说明": "—",
                "所属流程": "出行偏好信息子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 8,
                "接口名称": "获取员工各品类是否管控差标",
                "入参": "员工ID",
                "必要出参": "键值对（员工ID-是/否）",
                "出参说明": "—",
                "所属流程": "出行偏好信息子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 9,
                "接口名称": "获取企业POI",
                "入参": "企业ID",
                "必要出参": "公司地址、POI类型、所在城市、高德经纬度、百度经纬度",
                "出参说明": "—",
                "所属流程": "出行偏好信息子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 10,
                "接口名称": "获取企业参照人信息",
                "入参": "企业ID",
                "必要出参": "用户自主选择/固定位预订人",
                "出参说明": "获取指定员工的预订权限，根据公司预订权限、员工预订权限、员工代订权限计算所得的最终结果",
                "所属流程": "核心主流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 11,
                "接口名称": "获取出行人管控政策（预定管控）",
                "入参": "出行人员工ID",
                "必要出参": "预定管控政策明细，字段包含政策名称、需要差旅单预定、预订管控产品、使用差旅单差标、启用差旅单预订权限、启用差旅单预算管控、启用票数限制、启用房间数限制、启用用车次数限制、差旅单管控",
                "出参说明": "包含出行人且状态为“启用”的预定管控政策",
                "所属流程": "差旅管控子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 12,
                "接口名称": "获取出行人有效出差申请单",
                "入参": "出行人员工ID",
                "必要出参": "出差申请单内容，字段包含差旅单单号、差旅类型、申请时间、更新时间、操作类型、差旅内容、差旅开始时间、差旅结束时间、差旅地点（差旅出发地、差旅目的地、其他地点）、base地、差旅人信息、差旅信息、可预订产品、可预订数量、差旅政策代码（国内外机票、火车票、酒店、职级）、状态",
                "出参说明": "包含出行人且出差申请单结束时间>=当天的出差申请单",
                "所属流程": "差旅管控子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 13,
                "接口名称": "获取出行人管控政策简单（第一版暂不实施）",
                "入参": "员工ID",
                "必要出参": "申请管控/差旅单申请开关：开/关、外部对接开关：开/关",
                "出参说明": "—",
                "所属流程": "差旅管控子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 14,
                "接口名称": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）",
                "入参": "员工ID",
                "必要出参": "申请管控政策细则，字段包含政策名称、需要差旅单预定、可预订产品、出差事由是否必填、启用票数限制、启用房间数限制、启用用车次数限制",
                "出参说明": "",
                "所属流程": "创建差旅单子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 15,
                "接口名称": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）",
                "入参": "员工ID",
                "必要出参": "申请管控政策细则，字段包含政策名称、需要差旅单预定、可预订产品、出差事由是否必填、启用票数限制、启用房间数限制、启用用车次数限制",
                "出参说明": "",
                "所属流程": "创建差旅单子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 16,
                "接口名称": "创建出差申请单（内）（第一版暂不实施）",
                "入参": "差旅类型、出差事由、差旅范围、预订产品、出行人信息、差旅行程",
                "必要出参": "创建状态",
                "出参说明": "在外部创建=开的状态下，需要将出差申请单同步至外部",
                "所属流程": "创建差旅单子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 17,
                "接口名称": "创建出差申请单（外）（第一版暂不实施）",
                "入参": "出差申请单内容，字段包含差旅单单号、差旅类型、申请时间、更新时间、操作类型、差旅内容、差旅开始时间、差旅结束时间、差旅地点（差旅出发地、差旅目的地、其他地点）、base地、差旅人信息、差旅信息、可预订产品、可预订数量、差旅政策代码（国内外机票、火车票、酒店、职级）、状态",
                "必要出参": "创建状态",
                "出参说明": "在外部创建=开的状态下，需要将出差申请单同步至外部",
                "所属流程": "创建差旅单子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 18,
                "接口名称": "获取出行人当前位置",
                "入参": "出行人员工ID",
                "必要出参": "当前位置名称、经纬度",
                "出参说明": "—",
                "所属流程": "对话采集出行要素子流程、辅助要素补全子流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 19,
                "接口名称": "获取员工产品预订权限",
                "入参": "出行人员工ID",
                "必要出参": "可定品类",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 20,
                "接口名称": "获取出行人未出行订单-航班（第一版暂不实施）",
                "入参": "出行人员工ID",
                "必要出参": "订单号、订单类别、差旅类型（因私、因公）、关联ID、订单状态、出差申请单号、外部OA单号、航班号、航司二字码、出发机场所在城市名称、出发时间、完整到达时间、到达机场所在城市名称、订单总价格",
                "出参说明": "只返回已出票、出票中、待提交三种状态的订单",
                "所属流程": "辅助要素补全子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 21,
                "接口名称": "获取出行人未出行订单-火车票（第一版暂不实施）",
                "入参": "出行人员工ID",
                "必要出参": "订单号、订单类别、差旅类型（因私、因公）、关联ID、订单状态、出差申请单号、外部OA单号、车次号、出发城市、发车时间、到达时间、到达城市、坐席类别、座位号、票价",
                "出参说明": "只返回已出票、出票中、待提交三种状态的订单",
                "所属流程": "辅助要素补全子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 22,
                "接口名称": "获取出行人未出行订单-酒店（第一版暂不实施）",
                "入参": "出行人员工ID",
                "必要出参": "订单号、差旅类型（因私、因公）、订单状态、出差申请单号、外部OA单号、酒店名称、入住时间、离店时间、取消类型、限时取消时间、酒店地址、城市名称、房型名称、订单金额",
                "出参说明": "只返回订单状态为待入住、确认中、待提交三种状态的订单",
                "所属流程": "辅助要素补全子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 23,
                "接口名称": "获取出行人未出行订单-用车（第一版暂不实施）",
                "入参": "出行人员工ID",
                "必要出参": "订单号、订单类别、差旅类型（因私、因公）、订单状态、出差申请单号、外部OA单号、用车制度名称、用车事由描述、用车服务类型、预约出发时间、出发城市名称、到达城市名称、所需车型名称、出发地名称、出发经纬度、到达地名称、到达经纬度、总价",
                "出参说明": "只返回订单状态为待出行的订单",
                "所属流程": "辅助要素补全子流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 24,
                "接口名称": "获取出行人未使用机票（OPEN票）",
                "入参": "出行人员工ID",
                "必要出参": "航司名称、出发城市名称、到达城市名称、有效时间截止日期、状态",
                "出参说明": "—",
                "所属流程": "辅助要素补全子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 25,
                "接口名称": "获取出行人出发地用车与目的地用车历史订单",
                "入参": "出行人员工ID",
                "必要出参": "订单号、订单类别、差旅类型（因私、因公）、订单状态、出差申请单号、外部OA单号、用车制度名称、用车事由描述、用车服务类型、预约出发时间、出发城市名称、到达城市名称、所需车型名称、出发地名称、出发经纬度、到达地名称、到达经纬度、总价",
                "出参说明": "只返回订单状态为已完成的订单",
                "所属流程": "辅助要素补全子流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 26,
                "接口名称": "获取出行人所属公司授信/预存剩余额度是否>0",
                "入参": "公司ID",
                "必要出参": "是/否",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 27,
                "接口名称": "获取行程方案中地点与时间跨度的天气",
                "入参": "地点、日期、时间",
                "必要出参": "天气详情",
                "出参说明": "如有预警信息需返回",
                "所属流程": "核心主流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 28,
                "接口名称": "获取出行人历史相同行程订单-火车票",
                "入参": "出行人员工ID、出发城市、到达城市",
                "必要出参": "订单详情",
                "出参说明": "最近10条订单",
                "所属流程": "核心主流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 29,
                "接口名称": "获取出行人历史相同行程订单-机票",
                "入参": "出行人员工ID、出发城市、到达城市",
                "必要出参": "订单详情",
                "出参说明": "最近10条订单",
                "所属流程": "核心主流程",
                "优先级": "中",
                "计划交付时间": "2025-08-22"
        },
        {
                "接口编号": 30,
                "接口名称": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）",
                "入参": "出行人员工ID、城市名",
                "必要出参": "订单详情",
                "出参说明": "出发城市最近10条订单、目的地城市最近最近10条订单，如没有则返回出行人所属公司历史订单",
                "所属流程": "核心主流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 31,
                "接口名称": "获取航班搜索列表（需要包含舱位与可购保险信息）",
                "入参": "出行人员工ID、出发城市ID、到达城市ID、出发日期",
                "必要出参": "航班列表完整信息（包含舱位、保险）、是否超标、谁超标（多人场景）、超标原因、超标是否可订",
                "出参说明": "需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 32,
                "接口名称": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）",
                "入参": "出行人员工ID、出发城市ID、到达城市ID、出发日期",
                "必要出参": "车次搜索列表信息、是否超标、谁超标（多人场景）、超标原因、超标是否可订",
                "出参说明": "需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 33,
                "接口名称": "获取精选酒店列表",
                "入参": "员工ID、城市ID、地点名称、入住时间、离店时间",
                "必要出参": "酒店列表信息、酒店推荐逻辑",
                "出参说明": "需要增加返回酒店推荐逻辑，在list中需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益",
                "所属流程": "",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 34,
                "接口名称": "获取酒店搜索列表",
                "入参": "出行人员工ID、城市ID、地点名称、入住时间、离店时间",
                "必要出参": "酒店搜索列表信息、酒店排序模型计算公式+得分、是否超标、谁超标（多人场景）、超标原因、超标是否可订",
                "出参说明": "需要增加返回现有模型计算公式+结果信息，返回共20条信息，在20条信息中需要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 35,
                "接口名称": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）",
                "入参": "酒店ID、入店时间、离店时间",
                "必要出参": "酒店基本信息、酒店设施、酒店图片、房型政策信息、是否超标、谁超标（多人场景）、超标原因、超标是否可订",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 36,
                "接口名称": "获取用车列表（第一版暂不实施）",
                "入参": "出行人员工ID、用车制度名称、出发时间、出发地点、到达地点",
                "必要出参": "用车列表信息",
                "出参说明": "要返回最低价标签以及增加同程商旅积分计算+外部对接商城（如珑珠商城）积分计算逻辑，在接口中返回具体权益",
                "所属流程": "核心主流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 37,
                "接口名称": "获取出行人出差申请单状态",
                "入参": "出行人员工ID",
                "必要出参": "出差申请单单号、状态、审批人、审批信息",
                "出参说明": "—",
                "所属流程": "创单验证子流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 38,
                "接口名称": "获取出行人行程提交项启用状态",
                "入参": "出行人员工ID",
                "必要出参": "是/否",
                "出参说明": "—",
                "所属流程": "创单验证子流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 39,
                "接口名称": "获取结算金额信息",
                "入参": "产品提交预订信息",
                "必要出参": "总金额、支付方式、授信金额、个付金额、金额明细",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 40,
                "接口名称": "提交订单（支持多品类多订单）",
                "入参": "机票：航程类型、预定信息、预订乘客列表、联系人信息；\n酒店：城市、酒店ID、房型ID、政策ID、入住人、入离店时间等；\n火车票：请求key、车次号、车次类型、出发站、到达站等预订信息",
                "必要出参": "订单号",
                "出参说明": "一个行程多个订单，需要支持多品类一起下单",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 41,
                "接口名称": "申请改签机票订单",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 42,
                "接口名称": "确认改签机票订单",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 43,
                "接口名称": "变更酒店订单",
                "入参": "订单号、房间号、改期入住时间、改期离店时间、原因",
                "必要出参": "差旅订单号、订单号",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 44,
                "接口名称": "验证是否可改火车票订单",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 45,
                "接口名称": "申请改签火车票订单",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 46,
                "接口名称": "确认改签火车票订单",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "低",
                "计划交付时间": "2025-09-01"
        },
        {
                "接口编号": 47,
                "接口名称": "12306账号登录",
                "入参": "12306账号、密码、登录模式、验证码、订单号、登录类型",
                "必要出参": "登录状态、核验状态",
                "出参说明": "—",
                "所属流程": "",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 48,
                "接口名称": "12306常旅客信息查询",
                "入参": "12306账号、密码、查询类型",
                "必要出参": "姓名、证件号码、身份核验状态、是否可购票",
                "出参说明": "—",
                "所属流程": "",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 49,
                "接口名称": "提交用车订单（第一版暂不实施）",
                "入参": "预订信息",
                "必要出参": "状态信息、订单号",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "",
                "计划交付时间": ""
        },
        {
                "接口编号": 50,
                "接口名称": "提交火车票出票申请",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 51,
                "接口名称": "提交机票出票申请",
                "入参": "订单号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        },
        {
                "接口编号": 52,
                "接口名称": "提交保险订单",
                "入参": "航班号/车次号、日期、保险编号",
                "必要出参": "状态信息",
                "出参说明": "—",
                "所属流程": "核心主流程",
                "优先级": "高",
                "计划交付时间": "2025-08-11"
        }
];
        const apiSpecs = {
        "api_1": {
                "api_id": 1,
                "api_name": "员工信息同步（预订人）",
                "method": "POST",
                "endpoint": "/api/v1/employee/sync",
                "description": "员工信息同步（预订人）接口，用于同步和获取员工基本信息",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "sso_token": "string - 单点登录令牌"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "sso_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "employee_id": "string - 员工唯一标识",
                                                "enterprise_id": "string - 企业唯一标识",
                                                "is_express_booking": "boolean - 是否极速预订员工"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "employee_id": "EMP001",
                                                "enterprise_id": "ENT001",
                                                "is_express_booking": true
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_2": {
                "api_id": 2,
                "api_name": "获取员工代订类别",
                "method": "POST",
                "endpoint": "/api/v1/employee/booking-types",
                "description": "获取员工代订类别接口，用于获取员工可代订的人员类别范围",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "booking_types": "array<string> - 代订类别列表"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "booking_types": [
                                                        "本人",
                                                        "同事-所有员工"
                                                ]
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_3": {
                "api_id": 3,
                "api_name": "获取出行人详细信息",
                "method": "POST",
                "endpoint": "/api/v1/traveler/details",
                "description": "获取出行人详细信息接口，用于获取指定出行人的详细个人信息",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_name": "string - 出行人姓名",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_name": "张三",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "employee_id": "string - 员工唯一标识",
                                                "organization": "object - 组织架构信息",
                                                "vip_level": "string - VIP等级 (NONE/VIP1/VIP2/VIP3)",
                                                "contact": "object - 联系方式信息"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "employee_id": "EMP001",
                                                "organization": {
                                                        "department": "技术部",
                                                        "team": "后端开发组",
                                                        "level": "高级工程师"
                                                },
                                                "vip_level": "VIP1",
                                                "contact": {
                                                        "phone": "13800138000",
                                                        "email": "<EMAIL>"
                                                }
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_4": {
                "api_id": 4,
                "api_name": "是否在代订范围（判断出行人是否在预订人的代订范围）",
                "method": "POST",
                "endpoint": "/api/v1/booking/permission-check",
                "description": "是否在代订范围（判断出行人是否在预订人的代订范围）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "employee_id": "string - 员工唯一标识",
                                                "booking_permission": "boolean - 是否在可订范围"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "employee_id": "EMP001",
                                                "booking_permission": true
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_5": {
                "api_id": 5,
                "api_name": "获取出行人卡信息",
                "method": "POST",
                "endpoint": "/api/v1/traveler/cards",
                "description": "获取出行人卡信息接口，用于获取出行人的支付卡片信息",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "card_type": "string - 卡片类型",
                                                "card_number": "string - 卡号",
                                                "issuing_institution": "string - 发卡机构"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "card_type": "信用卡",
                                                "card_number": "****1234",
                                                "issuing_institution": "招商银行"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_6": {
                "api_id": 6,
                "api_name": "获取员工喜好",
                "method": "POST",
                "endpoint": "/api/v1/employee/preferences",
                "description": "获取员工喜好接口，用于获取员工的出行偏好设置",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_7": {
                "api_id": 7,
                "api_name": "获取员工是否高消费",
                "method": "POST",
                "endpoint": "/api/v1/employee/high-consumption",
                "description": "获取员工是否高消费接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "employee_id": "string - 员工唯一标识"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "employee_id": "EMP001"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_8": {
                "api_id": 8,
                "api_name": "获取员工各品类是否管控差标",
                "method": "POST",
                "endpoint": "/api/v1/employee/category-controls",
                "description": "获取员工各品类是否管控差标接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "employee_id": "string - 员工唯一标识"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "employee_id": "EMP001"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_9": {
                "api_id": 9,
                "api_name": "获取企业POI",
                "method": "POST",
                "endpoint": "/api/v1/enterprise/poi",
                "description": "获取企业POI接口，用于获取企业相关的地理位置信息",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "enterprise_id": "string - 企业唯一标识"
                        },
                        "example": {
                                "enterprise_id": "ENT001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "poi_list": "array<object> - POI地址列表",
                                                "coordinates": "object - 坐标信息"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "poi_list": [
                                                        {
                                                                "name": "公司总部",
                                                                "address": "北京市朝阳区xxx路xxx号",
                                                                "coordinates": {
                                                                        "longitude": 116.397128,
                                                                        "latitude": 39.916527
                                                                }
                                                        }
                                                ],
                                                "coordinates": "sample_value"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_10": {
                "api_id": 10,
                "api_name": "获取企业参照人信息",
                "method": "POST",
                "endpoint": "/api/v1/enterprise/reference-person",
                "description": "获取企业参照人信息接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "enterprise_id": "string - 企业唯一标识"
                        },
                        "example": {
                                "enterprise_id": "ENT001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_11": {
                "api_id": 11,
                "api_name": "获取出行人管控政策（预定管控）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人管控政策（预定管控）",
                "description": "获取出行人管控政策（预定管控）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "policies": "array<object> - 政策列表",
                                                "expense_standards": "object - 差旅标准"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "policies": [
                                                        {
                                                                "policy_id": "POL001",
                                                                "policy_name": "标准差旅政策",
                                                                "effective_date": "2025-01-01"
                                                        }
                                                ],
                                                "expense_standards": "sample_value"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_12": {
                "api_id": 12,
                "api_name": "获取出行人有效出差申请单",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人有效出差申请单",
                "description": "获取出行人有效出差申请单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "policies": "array<object> - 政策列表"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "policies": [
                                                        {
                                                                "policy_id": "POL001",
                                                                "policy_name": "标准差旅政策",
                                                                "effective_date": "2025-01-01"
                                                        }
                                                ]
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_13": {
                "api_id": 13,
                "api_name": "获取出行人管控政策简单（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人管控政策简单（第一版暂不实施）",
                "description": "获取出行人管控政策简单（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_14": {
                "api_id": 14,
                "api_name": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）",
                "description": "获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "policies": "array<object> - 政策列表"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "policies": [
                                                        {
                                                                "policy_id": "POL001",
                                                                "policy_name": "标准差旅政策",
                                                                "effective_date": "2025-01-01"
                                                        }
                                                ]
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_15": {
                "api_id": 15,
                "api_name": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）",
                "description": "获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "policies": "array<object> - 政策列表"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "policies": [
                                                        {
                                                                "policy_id": "POL001",
                                                                "policy_name": "标准差旅政策",
                                                                "effective_date": "2025-01-01"
                                                        }
                                                ]
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_16": {
                "api_id": 16,
                "api_name": "创建出差申请单（内）（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/create/出差申请单（内）（第一版暂不实施）",
                "description": "创建出差申请单（内）（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_17": {
                "api_id": 17,
                "api_name": "创建出差申请单（外）（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/create/出差申请单（外）（第一版暂不实施）",
                "description": "创建出差申请单（外）（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "order_id": "string - 差旅单ID",
                                "policy_id": "string - 政策ID",
                                "departure": "string - 出发地",
                                "destination": "string - 目的地"
                        },
                        "example": {
                                "order_id": "TO2025073100001",
                                "policy_id": "POL001",
                                "departure": "北京",
                                "destination": "上海"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_18": {
                "api_id": 18,
                "api_name": "获取出行人当前位置",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人当前位置",
                "description": "获取出行人当前位置接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "coordinates": "object - 坐标信息"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "coordinates": "sample_value"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_19": {
                "api_id": 19,
                "api_name": "获取员工产品预订权限",
                "method": "POST",
                "endpoint": "/api/v1/get/员工产品预订权限",
                "description": "获取员工产品预订权限接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_20": {
                "api_id": 20,
                "api_name": "获取出行人未出行订单-航班（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人未出行订单-航班（第一版暂不实施）",
                "description": "获取出行人未出行订单-航班（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_21": {
                "api_id": 21,
                "api_name": "获取出行人未出行订单-火车票（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人未出行订单-火车票（第一版暂不实施）",
                "description": "获取出行人未出行订单-火车票（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_22": {
                "api_id": 22,
                "api_name": "获取出行人未出行订单-酒店（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人未出行订单-酒店（第一版暂不实施）",
                "description": "获取出行人未出行订单-酒店（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "poi_list": "array<object> - POI地址列表"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "poi_list": [
                                                        {
                                                                "name": "公司总部",
                                                                "address": "北京市朝阳区xxx路xxx号",
                                                                "coordinates": {
                                                                        "longitude": 116.397128,
                                                                        "latitude": 39.916527
                                                                }
                                                        }
                                                ]
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_23": {
                "api_id": 23,
                "api_name": "获取出行人未出行订单-用车（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人未出行订单-用车（第一版暂不实施）",
                "description": "获取出行人未出行订单-用车（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "coordinates": "object - 坐标信息"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "coordinates": "sample_value"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_24": {
                "api_id": 24,
                "api_name": "获取出行人未使用机票（OPEN票）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人未使用机票（open票）",
                "description": "获取出行人未使用机票（OPEN票）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_25": {
                "api_id": 25,
                "api_name": "获取出行人出发地用车与目的地用车历史订单",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人出发地用车与目的地用车历史订单",
                "description": "获取出行人出发地用车与目的地用车历史订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "coordinates": "object - 坐标信息"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "coordinates": "sample_value"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_26": {
                "api_id": 26,
                "api_name": "获取出行人所属公司授信/预存剩余额度是否>0",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人所属公司授信/预存剩余额度是否>0",
                "description": "获取出行人所属公司授信/预存剩余额度是否>0接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_27": {
                "api_id": 27,
                "api_name": "获取行程方案中地点与时间跨度的天气",
                "method": "POST",
                "endpoint": "/api/v1/get/行程方案中地点与时间跨度的天气",
                "description": "获取行程方案中地点与时间跨度的天气接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_28": {
                "api_id": 28,
                "api_name": "获取出行人历史相同行程订单-火车票",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人历史相同行程订单-火车票",
                "description": "获取出行人历史相同行程订单-火车票接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_29": {
                "api_id": 29,
                "api_name": "获取出行人历史相同行程订单-机票",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人历史相同行程订单-机票",
                "description": "获取出行人历史相同行程订单-机票接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_30": {
                "api_id": 30,
                "api_name": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）",
                "description": "获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_31": {
                "api_id": 31,
                "api_name": "获取航班搜索列表（需要包含舱位与可购保险信息）",
                "method": "POST",
                "endpoint": "/api/v1/get/航班搜索列表（需要包含舱位与可购保险信息）",
                "description": "获取航班搜索列表（需要包含舱位与可购保险信息）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_32": {
                "api_id": 32,
                "api_name": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）",
                "method": "POST",
                "endpoint": "/api/v1/get/车次搜索列表（需要包含各座位类型余票与可购保险信息）",
                "description": "获取车次搜索列表（需要包含各座位类型余票与可购保险信息）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_33": {
                "api_id": 33,
                "api_name": "获取精选酒店列表",
                "method": "POST",
                "endpoint": "/api/v1/get/精选酒店列表",
                "description": "获取精选酒店列表接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识"
                        },
                        "example": {
                                "employee_id": "EMP001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_34": {
                "api_id": 34,
                "api_name": "获取酒店搜索列表",
                "method": "POST",
                "endpoint": "/api/v1/get/酒店搜索列表",
                "description": "获取酒店搜索列表接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_35": {
                "api_id": 35,
                "api_name": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）",
                "method": "POST",
                "endpoint": "/api/v1/get/酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）",
                "description": "获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "policies": "array<object> - 政策列表"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "policies": [
                                                        {
                                                                "policy_id": "POL001",
                                                                "policy_name": "标准差旅政策",
                                                                "effective_date": "2025-01-01"
                                                        }
                                                ]
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_36": {
                "api_id": 36,
                "api_name": "获取用车列表（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/get/用车列表（第一版暂不实施）",
                "description": "获取用车列表（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表",
                                "departure": "string - 出发地",
                                "departure_time": "string - 出发时间 (YYYY-MM-DD HH:mm:ss)"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ],
                                "departure": "北京",
                                "departure_time": "2025-08-15 09:00:00"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_37": {
                "api_id": 37,
                "api_name": "获取出行人出差申请单状态",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人出差申请单状态",
                "description": "获取出行人出差申请单状态接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_38": {
                "api_id": 38,
                "api_name": "获取出行人行程提交项启用状态",
                "method": "POST",
                "endpoint": "/api/v1/get/出行人行程提交项启用状态",
                "description": "获取出行人行程提交项启用状态接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "employee_id": "string - 员工唯一标识",
                                "traveler_employee_ids": "array<string> - 出行人员工ID列表"
                        },
                        "example": {
                                "employee_id": "EMP001",
                                "traveler_employee_ids": [
                                        "EMP001",
                                        "EMP002"
                                ]
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_39": {
                "api_id": 39,
                "api_name": "获取结算金额信息",
                "method": "POST",
                "endpoint": "/api/v1/get/结算金额信息",
                "description": "获取结算金额信息接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_40": {
                "api_id": 40,
                "api_name": "提交订单（支持多品类多订单）",
                "method": "POST",
                "endpoint": "/api/v1/提交订单（支持多品类多订单）",
                "description": "提交订单（支持多品类多订单）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {
                                "policy_id": "string - 政策ID"
                        },
                        "example": {
                                "policy_id": "POL001"
                        }
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_41": {
                "api_id": 41,
                "api_name": "申请改签机票订单",
                "method": "POST",
                "endpoint": "/api/v1/申请改签机票订单",
                "description": "申请改签机票订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_42": {
                "api_id": 42,
                "api_name": "确认改签机票订单",
                "method": "POST",
                "endpoint": "/api/v1/确认改签机票订单",
                "description": "确认改签机票订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_43": {
                "api_id": 43,
                "api_name": "变更酒店订单",
                "method": "POST",
                "endpoint": "/api/v1/变更酒店订单",
                "description": "变更酒店订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_44": {
                "api_id": 44,
                "api_name": "验证是否可改火车票订单",
                "method": "POST",
                "endpoint": "/api/v1/验证是否可改火车票订单",
                "description": "验证是否可改火车票订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_45": {
                "api_id": 45,
                "api_name": "申请改签火车票订单",
                "method": "POST",
                "endpoint": "/api/v1/申请改签火车票订单",
                "description": "申请改签火车票订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_46": {
                "api_id": 46,
                "api_name": "确认改签火车票订单",
                "method": "POST",
                "endpoint": "/api/v1/确认改签火车票订单",
                "description": "确认改签火车票订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_47": {
                "api_id": 47,
                "api_name": "12306账号登录",
                "method": "POST",
                "endpoint": "/api/v1/12306账号登录",
                "description": "12306账号登录接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_48": {
                "api_id": 48,
                "api_name": "12306常旅客信息查询",
                "method": "POST",
                "endpoint": "/api/v1/12306常旅客信息查询",
                "description": "12306常旅客信息查询接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {
                                                "employee_name": "string - 员工姓名"
                                        },
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {
                                                "employee_name": "张三"
                                        },
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_49": {
                "api_id": 49,
                "api_name": "提交用车订单（第一版暂不实施）",
                "method": "POST",
                "endpoint": "/api/v1/提交用车订单（第一版暂不实施）",
                "description": "提交用车订单（第一版暂不实施）接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_50": {
                "api_id": 50,
                "api_name": "提交火车票出票申请",
                "method": "POST",
                "endpoint": "/api/v1/提交火车票出票申请",
                "description": "提交火车票出票申请接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_51": {
                "api_id": 51,
                "api_name": "提交机票出票申请",
                "method": "POST",
                "endpoint": "/api/v1/提交机票出票申请",
                "description": "提交机票出票申请接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        },
        "api_52": {
                "api_id": 52,
                "api_name": "提交保险订单",
                "method": "POST",
                "endpoint": "/api/v1/提交保险订单",
                "description": "提交保险订单接口，用于相关业务处理",
                "request": {
                        "headers": {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer {access_token}",
                                "X-Enterprise-ID": "{enterprise_id}"
                        },
                        "body_schema": {},
                        "example": {}
                },
                "response": {
                        "success": {
                                "schema": {
                                        "code": "int - 状态码",
                                        "message": "string - 响应消息",
                                        "data": {},
                                        "timestamp": "string - 响应时间"
                                },
                                "example": {
                                        "code": 200,
                                        "message": "success",
                                        "data": {},
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        },
                        "error": {
                                "example": {
                                        "code": 400,
                                        "message": "参数错误",
                                        "data": null,
                                        "timestamp": "2025-07-31 14:30:25"
                                }
                        }
                }
        }
};
        
        // Group APIs by workflow
        function groupApisByWorkflow() {
            const workflows = {};
            apiData.forEach(api => {
                const workflow = api['所属流程'] || '其他';
                if (!workflows[workflow]) {
                    workflows[workflow] = [];
                }
                workflows[workflow].push(api);
            });
            return workflows;
        }
        
        // Generate navigation
        function generateNavigation() {
            const navigation = document.getElementById('apiNavigation');
            const workflows = groupApisByWorkflow();
            
            Object.keys(workflows).sort().forEach(workflow => {
                const li = document.createElement('li');
                li.innerHTML = `<a href="#workflow-${workflow}">${workflow}<span class="workflow-count">${workflows[workflow].length}</span></a>`;
                navigation.appendChild(li);
            });
        }
        
        // Generate API list with detailed JSON examples
        function generateApiList() {
            const apiList = document.getElementById('apiList');
            const workflows = groupApisByWorkflow();
            
            Object.keys(workflows).sort().forEach(workflow => {
                const section = document.createElement('div');
                section.className = 'api-section';
                section.id = `workflow-${workflow}`;
                
                section.innerHTML = `
                    <h2>📋 ${workflow} (${workflows[workflow].length}个接口)</h2>
                    ${workflows[workflow].map(api => generateDetailedApiItem(api)).join('')}
                `;
                
                apiList.appendChild(section);
            });
        }
        
        // Generate detailed API item with JSON examples
        function generateDetailedApiItem(api) {
            const priorityClass = api['优先级'] === '高' ? 'priority-high' : 
                                api['优先级'] === '中' ? 'priority-medium' : 'priority-low';
            
            const apiId = `api-${api['接口编号']}`;
            const specKey = `api_${api['接口编号']}`;
            const spec = apiSpecs[specKey];
            
            if (!spec) return '';
            
            return `
                <div class="api-item">
                    <div class="api-header" onclick="toggleApi('${apiId}')">
                        <div>
                            <span class="api-id">API-${api['接口编号']}</span>
                            <span class="api-method">${spec.method}</span>
                            <span class="api-title">${api['接口名称']}</span>
                        </div>
                        <div>
                            <span class="api-priority ${priorityClass}">${api['优先级']}优先级</span>
                            <button class="toggle-btn">▼</button>
                        </div>
                    </div>
                    <div class="api-content" id="${apiId}">
                        <div class="endpoint-info">
                            <p><strong>接口地址:</strong> <code>${spec.method} ${spec.endpoint}</code></p>
                            <p><strong>接口描述:</strong> ${spec.description}</p>
                        </div>
                        
                        <div class="section-tabs">
                            <div class="tab active" onclick="switchTab(event, '${apiId}-overview')">概览</div>
                            <div class="tab" onclick="switchTab(event, '${apiId}-request')">请求参数</div>
                            <div class="tab" onclick="switchTab(event, '${apiId}-response')">响应参数</div>
                            <div class="tab" onclick="switchTab(event, '${apiId}-examples')">示例</div>
                        </div>
                        
                        <div id="${apiId}-overview" class="tab-content active">
                            <h4>📋 接口概览</h4>
                            <table class="param-table">
                                <tr><td><strong>接口名称</strong></td><td>${api['接口名称']}</td></tr>
                                <tr><td><strong>请求方法</strong></td><td><code>${spec.method}</code></td></tr>
                                <tr><td><strong>接口地址</strong></td><td><code>${spec.endpoint}</code></td></tr>
                                <tr><td><strong>所属流程</strong></td><td>${api['所属流程']}</td></tr>
                                <tr><td><strong>优先级</strong></td><td>${api['优先级']}</td></tr>
                                <tr><td><strong>计划交付</strong></td><td>${api['计划交付时间']}</td></tr>
                            </table>
                        </div>
                        
                        <div id="${apiId}-request" class="tab-content">
                            <h4>📤 请求参数</h4>
                            <h5>Headers</h5>
                            <table class="param-table">
                                <thead>
                                    <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td><code>Content-Type</code></td><td>string</td><td>是</td><td>application/json</td></tr>
                                    <tr><td><code>Authorization</code></td><td>string</td><td>是</td><td>Bearer {access_token}</td></tr>
                                    <tr><td><code>X-Enterprise-ID</code></td><td>string</td><td>是</td><td>企业ID</td></tr>
                                </tbody>
                            </table>
                            
                            <h5>Body Parameters</h5>
                            ${generateParamTable(spec.request.body_schema)}
                        </div>
                        
                        <div id="${apiId}-response" class="tab-content">
                            <h4>📥 响应参数</h4>
                            <h5>成功响应</h5>
                            ${generateParamTable(spec.response.success.schema)}
                        </div>
                        
                        <div id="${apiId}-examples" class="tab-content">
                            <h4>💡 请求示例</h4>
                            ${generateJsonExample('请求示例', JSON.stringify(spec.request.example, null, 2))}
                            
                            <h4>✅ 成功响应示例</h4>
                            ${generateJsonExample('成功响应', JSON.stringify(spec.response.success.example, null, 2))}
                            
                            <h4>❌ 错误响应示例</h4>
                            ${generateJsonExample('错误响应', JSON.stringify(spec.response.error.example, null, 2))}
                        </div>
                        
                        <div class="meta-info">
                            <div class="meta-item">
                                <span>🎯</span>
                                <span>所属流程: ${api['所属流程']}</span>
                            </div>
                            <div class="meta-item">
                                <span>📅</span>
                                <span>计划交付: ${api['计划交付时间']}</span>
                            </div>
                            <div class="meta-item">
                                <span>⚡</span>
                                <span>优先级: ${api['优先级']}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Generate parameter table
        function generateParamTable(schema) {
            if (!schema || Object.keys(schema).length === 0) {
                return '<p>无参数</p>';
            }
            
            let table = `
                <table class="param-table">
                    <thead>
                        <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                    </thead>
                    <tbody>
            `;
            
            Object.entries(schema).forEach(([key, desc]) => {
                // Handle cases where desc might not be a string or might not contain ' - '
                let type = 'string';
                let description = '';
                
                if (typeof desc === 'string' && desc.includes(' - ')) {
                    const parts = desc.split(' - ');
                    type = parts[0] || 'string';
                    description = parts.slice(1).join(' - ') || '';
                } else if (typeof desc === 'string') {
                    description = desc;
                } else {
                    description = JSON.stringify(desc);
                }
                
                table += `<tr><td><code>${key}</code></td><td>${type}</td><td>是</td><td>${description}</td></tr>`;
            });
            
            table += '</tbody></table>';
            return table;
        }
        
        // Generate JSON example
        function generateJsonExample(title, jsonContent) {
            const id = 'json_' + Math.random().toString(36).substr(2, 9);
            return `
                <div class="json-example">
                    <div class="json-header">
                        ${title}
                        <button class="copy-btn" onclick="copyToClipboard('${id}')" title="复制代码">复制</button>
                    </div>
                    <div class="json-content" id="${id}">${escapeHtml(jsonContent)}</div>
                </div>
            `;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Toggle API content visibility
        function toggleApi(apiId) {
            const content = document.getElementById(apiId);
            const isActive = content.classList.contains('active');
            
            // Close all other API contents
            document.querySelectorAll('.api-content.active').forEach(el => {
                el.classList.remove('active');
                el.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▼';
            });
            
            // Toggle current API content
            if (!isActive) {
                content.classList.add('active');
                content.previousElementSibling.querySelector('.toggle-btn').innerHTML = '▲';
            }
        }
        
        // Switch tabs
        function switchTab(event, tabId) {
            const clickedTab = event.target;
            const apiContent = clickedTab.closest('.api-content');
            
            // Remove active class from all tabs and tab contents in this API
            apiContent.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            apiContent.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            clickedTab.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }
        
        // Copy to clipboard
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                // Show temporary feedback
                const button = element.parentElement.querySelector('.copy-btn');
                const originalText = button.textContent;
                button.textContent = '已复制!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            });
        }
        
        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const apiItems = document.querySelectorAll('.api-item');
                
                apiItems.forEach(item => {
                    const title = item.querySelector('.api-title').textContent.toLowerCase();
                    const content = item.textContent.toLowerCase();
                    
                    if (title.includes(query) || content.includes(query)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Show/hide sections based on visible items
                const sections = document.querySelectorAll('.api-section');
                sections.forEach(section => {
                    const visibleItems = section.querySelectorAll('.api-item[style="display: block;"], .api-item:not([style*="display: none"])');
                    if (visibleItems.length > 0 || !query) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });
            });
        }
        
        // Initialize the documentation
        document.addEventListener('DOMContentLoaded', function() {
            generateNavigation();
            generateApiList();
            setupSearch();
            
            // Add smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>