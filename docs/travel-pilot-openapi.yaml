openapi: 3.0.3
info:
  title: Travel Pilot API
  description: 企业差旅管理系统接口规范
  version: 1.0.0
  contact:
    name: Travel Pilot API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
servers:
- url: https://api.travelpilot.com/v1
  description: 生产环境
- url: https://staging-api.travelpilot.com/v1
  description: 测试环境
- url: https://dev-api.travelpilot.com/v1
  description: 开发环境
security:
- BearerAuth: []
tags:
- name: authentication
  description: 身份认证相关接口
- name: employee
  description: 员工管理相关接口
- name: traveler
  description: 出行人管理相关接口
- name: booking
  description: 预订管理相关接口
- name: policy
  description: 差旅政策相关接口
- name: travel-order
  description: 差旅单管理相关接口
- name: enterprise
  description: 企业管理相关接口
paths:
  /api/v1/employee/sync:
    post:
      tags:
      - employee
      summary: 员工信息同步（预订人）
      description: 员工信息同步（预订人）接口，用于同步和获取员工基本信息
      operationId: api_1_post__api_v1_employee_sync
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_id:
                          type: string
                          description: 员工唯一标识符
                          example: EMP001
                          pattern: ^EMP[0-9]{3,}$
                        enterprise_id:
                          type: string
                          description: 企业唯一标识符
                          example: ENT001
                          pattern: ^ENT[0-9]{3,}$
                        is_express_booking:
                          type: boolean
                          description: 是否为极速预订员工
                          example: true
              example:
                code: 200
                message: success
                data:
                  employee_id: EMP001
                  enterprise_id: ENT001
                  is_express_booking: true
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                sso_token:
                  type: string
                  description: 单点登录认证令牌
                  example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
                  minLength: 100
              required:
              - employee_id
              - sso_token
            example:
              employee_id: EMP001
              sso_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
  /api/v1/employee/booking-types:
    post:
      tags:
      - employee
      summary: 获取员工代订类别
      description: 获取员工代订类别接口，用于获取员工可代订的人员类别范围
      operationId: api_2_post__api_v1_employee_booking_types
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        booking_types:
                          type: array
                          description: 可代订人员类别列表
                          example:
                          - 本人
                          - 同事-所有员工
                          items:
                            type: string
                            enum:
                            - 本人
                            - 同事-所有员工
                            - 同事-自定义范围
                            - 客人
              example:
                code: 200
                message: success
                data:
                  booking_types:
                  - 本人
                  - 同事-所有员工
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/traveler/details:
    post:
      tags:
      - traveler
      summary: 获取出行人详细信息
      description: 获取出行人详细信息接口，用于获取指定出行人的详细个人信息
      operationId: api_3_post__api_v1_traveler_details
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_id:
                          type: string
                          description: 员工唯一标识符
                          example: EMP001
                          pattern: ^EMP[0-9]{3,}$
                        organization:
                          type: object
                          description: 员工组织架构信息
                          example:
                            company_id: COMP001
                            company_name: 科技有限公司
                            department_id: DEPT001
                            department_name: 技术部
                            team_id: TEAM001
                            team_name: 后端开发组
                            job_title: 高级工程师
                            job_level: P7
                            manager_id: EMP100
                            cost_center: CC001
                          properties:
                            company_id:
                              type: string
                              description: 公司ID
                              example: COMP001
                            company_name:
                              type: string
                              description: 公司名称
                              example: 科技有限公司
                            department_id:
                              type: string
                              description: 部门ID
                              example: DEPT001
                            department_name:
                              type: string
                              description: 部门名称
                              example: 技术部
                            team_id:
                              type: string
                              description: 团队ID
                              example: TEAM001
                            team_name:
                              type: string
                              description: 团队名称
                              example: 后端开发组
                            job_title:
                              type: string
                              description: 职位名称
                              example: 高级工程师
                            job_level:
                              type: string
                              description: 职级
                              example: P7
                            manager_id:
                              type: string
                              description: 直属领导员工ID
                              example: EMP100
                            cost_center:
                              type: string
                              description: 成本中心
                              example: CC001
                        vip_level:
                          type: string
                          description: VIP等级
                          example: VIP1
                          enum:
                          - NONE
                          - VIP1
                          - VIP2
                          - VIP3
                        contact:
                          type: object
                          description: 联系方式信息
                          example:
                            mobile: '13800138000'
                            email: <EMAIL>
                            work_phone: 010-12345678
                            emergency_contact:
                              name: 李四
                              phone: '13900139000'
                              relationship: 配偶
                          properties:
                            mobile:
                              type: string
                              description: 手机号码
                              example: '13800138000'
                              pattern: ^1[3-9]\d{9}$
                            email:
                              type: string
                              description: 邮箱地址
                              example: <EMAIL>
                              format: email
                            work_phone:
                              type: string
                              description: 工作电话
                              example: 010-12345678
                            emergency_contact:
                              type: object
                              description: 紧急联系人
                              properties:
                                name:
                                  type: string
                                  description: 紧急联系人姓名
                                  example: 李四
                                phone:
                                  type: string
                                  description: 紧急联系人电话
                                  example: '13900139000'
                                relationship:
                                  type: string
                                  description: 关系
                                  example: 配偶
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  employee_id: EMP001
                  organization:
                    company_id: COMP001
                    company_name: 科技有限公司
                    department_id: DEPT001
                    department_name: 技术部
                    team_id: TEAM001
                    team_name: 后端开发组
                    job_title: 高级工程师
                    job_level: P7
                    manager_id: EMP100
                    cost_center: CC001
                  vip_level: VIP1
                  contact:
                    mobile: '13800138000'
                    email: <EMAIL>
                    work_phone: 010-12345678
                    emergency_contact:
                      name: 李四
                      phone: '13900139000'
                      relationship: 配偶
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_name:
                  type: string
                  description: 出行人员姓名
                  example: 张三
                  minLength: 2
                  maxLength: 50
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
              - traveler_name
            example:
              employee_id: EMP001
              traveler_name: 张三
              traveler_employee_ids:
              - EMP001
              - EMP002
              page: 1
              page_size: 20
  /api/v1/booking/permission-check:
    post:
      tags:
      - traveler
      summary: 是否在代订范围（判断出行人是否在预订人的代订范围）
      description: 是否在代订范围（判断出行人是否在预订人的代订范围）接口，用于相关业务处理
      operationId: api_4_post__api_v1_booking_permission_check
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_id:
                          type: string
                          description: 员工唯一标识符
                          example: EMP001
                          pattern: ^EMP[0-9]{3,}$
                        booking_permissions:
                          type: array
                          description: 代订权限详情
                          example:
                          - target_employee_id: EMP002
                            target_employee_name: 李四
                            can_book: true
                            permission_reason: 同部门员工
                          items:
                            type: object
                            properties:
                              target_employee_id:
                                type: string
                                description: 目标员工ID
                                example: EMP002
                              target_employee_name:
                                type: string
                                description: 目标员工姓名
                                example: 李四
                              can_book:
                                type: boolean
                                description: 是否可以代订
                                example: true
                              permission_reason:
                                type: string
                                description: 权限原因
                                example: 同部门员工
              example:
                code: 200
                message: success
                data:
                  employee_id: EMP001
                  booking_permissions:
                  - target_employee_id: EMP002
                    target_employee_name: 李四
                    can_book: true
                    permission_reason: 同部门员工
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/traveler/cards:
    post:
      tags:
      - traveler
      summary: 获取出行人卡信息
      description: 获取出行人卡信息接口，用于获取出行人的支付卡片信息
      operationId: api_5_post__api_v1_traveler_cards
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        cards:
                          type: array
                          description: 支付卡片信息列表
                          example:
                          - card_id: CARD001
                            card_type: 信用卡
                            card_number: '****1234'
                            card_holder: 张三
                            issuing_bank: 招商银行
                            is_default: true
                            is_corporate: false
                            status: active
                          items:
                            type: object
                            properties:
                              card_id:
                                type: string
                                description: 卡片ID
                                example: CARD001
                              card_type:
                                type: string
                                description: 卡片类型
                                example: 信用卡
                                enum:
                                - 信用卡
                                - 借记卡
                                - 企业卡
                                - 预付卡
                              card_number:
                                type: string
                                description: 卡号（脱敏显示）
                                example: '****1234'
                                pattern: \*{4}\d{4}
                              card_holder:
                                type: string
                                description: 持卡人姓名
                                example: 张三
                              issuing_bank:
                                type: string
                                description: 发卡银行
                                example: 招商银行
                              is_default:
                                type: boolean
                                description: 是否为默认卡片
                                example: true
                              is_corporate:
                                type: boolean
                                description: 是否为企业卡
                                example: false
                              status:
                                type: string
                                description: 卡片状态
                                example: active
                                enum:
                                - active
                                - inactive
                                - expired
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  cards:
                  - card_id: CARD001
                    card_type: 信用卡
                    card_number: '****1234'
                    card_holder: 张三
                    issuing_bank: 招商银行
                    is_default: true
                    is_corporate: false
                    status: active
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
            example:
              employee_id: EMP001
              page: 1
              page_size: 20
  /api/v1/employee/preferences:
    post:
      tags:
      - employee
      summary: 获取员工喜好
      description: 获取员工喜好接口，用于获取员工的出行偏好设置
      operationId: api_6_post__api_v1_employee_preferences
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/employee/high-consumption:
    post:
      tags:
      - employee
      summary: 获取员工是否高消费
      description: 获取员工是否高消费接口，用于相关业务处理
      operationId: api_7_post__api_v1_employee_high_consumption
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_id:
                          type: string
                          description: 员工唯一标识符
                          example: EMP001
                          pattern: ^EMP[0-9]{3,}$
              example:
                code: 200
                message: success
                data:
                  employee_id: EMP001
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/employee/category-controls:
    post:
      tags:
      - employee
      summary: 获取员工各品类是否管控差标
      description: 获取员工各品类是否管控差标接口，用于相关业务处理
      operationId: api_8_post__api_v1_employee_category_controls
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_id:
                          type: string
                          description: 员工唯一标识符
                          example: EMP001
                          pattern: ^EMP[0-9]{3,}$
              example:
                code: 200
                message: success
                data:
                  employee_id: EMP001
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/enterprise/poi:
    post:
      tags:
      - enterprise
      summary: 获取企业POI
      description: 获取企业POI接口，用于获取企业相关的地理位置信息
      operationId: api_9_post__api_v1_enterprise_poi
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        poi_list:
                          type: array
                          description: POI地址信息列表
                          example:
                          - poi_id: POI001
                            poi_name: 公司总部
                            poi_type: office
                            address: 北京市朝阳区xxx路xxx号
                            city: 北京
                            coordinates:
                              longitude: 116.397128
                              latitude: 39.916527
                              coordinate_system: GCJ02
                          items:
                            type: object
                            properties:
                              poi_id:
                                type: string
                                description: POI唯一标识
                                example: POI001
                              poi_name:
                                type: string
                                description: POI名称
                                example: 公司总部
                              poi_type:
                                type: string
                                description: POI类型
                                example: office
                                enum:
                                - office
                                - hotel
                                - airport
                                - station
                                - other
                              address:
                                type: string
                                description: 详细地址
                                example: 北京市朝阳区xxx路xxx号
                              city:
                                type: string
                                description: 所在城市
                                example: 北京
                              coordinates:
                                type: object
                                description: 坐标信息
                                properties:
                                  longitude:
                                    type: number
                                    description: 经度
                                    example: 116.397128
                                  latitude:
                                    type: number
                                    description: 纬度
                                    example: 39.916527
                                  coordinate_system:
                                    type: string
                                    description: 坐标系统
                                    example: GCJ02
                                    enum:
                                    - WGS84
                                    - GCJ02
                                    - BD09
              example:
                code: 200
                message: success
                data:
                  poi_list:
                  - poi_id: POI001
                    poi_name: 公司总部
                    poi_type: office
                    address: 北京市朝阳区xxx路xxx号
                    city: 北京
                    coordinates:
                      longitude: 116.397128
                      latitude: 39.916527
                      coordinate_system: GCJ02
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                enterprise_id:
                  type: string
                  description: 企业唯一标识符
                  example: ENT001
                  pattern: ^ENT[0-9]{3,}$
              required:
              - enterprise_id
            example:
              enterprise_id: ENT001
  /api/v1/enterprise/reference-person:
    post:
      tags:
      - enterprise
      summary: 获取企业参照人信息
      description: 获取企业参照人信息接口，用于相关业务处理
      operationId: api_10_post__api_v1_enterprise_reference_person
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                enterprise_id:
                  type: string
                  description: 企业唯一标识符
                  example: ENT001
                  pattern: ^ENT[0-9]{3,}$
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - enterprise_id
            example:
              enterprise_id: ENT001
              page: 1
              page_size: 20
  /api/v1/出行人管控政策（预定管控）:
    post:
      tags:
      - traveler
      summary: 获取出行人管控政策（预定管控）
      description: 获取出行人管控政策（预定管控）接口，用于相关业务处理
      operationId: api_11_post__api_v1_出行人管控政策（预定管控）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        policies:
                          type: array
                          description: 差旅政策列表
                          example:
                          - policy_id: POL001
                            policy_name: 标准差旅政策
                            policy_type: flight
                            effective_date: '2025-01-01'
                            expiry_date: '2025-12-31'
                            rules:
                              max_amount: 5000.0
                              booking_advance_days: 3
                              approval_required: true
                          items:
                            type: object
                            properties:
                              policy_id:
                                type: string
                                description: 政策ID
                                example: POL001
                              policy_name:
                                type: string
                                description: 政策名称
                                example: 标准差旅政策
                              policy_type:
                                type: string
                                description: 政策类型
                                example: flight
                                enum:
                                - flight
                                - hotel
                                - train
                                - taxi
                                - meal
                              effective_date:
                                type: string
                                description: 生效日期
                                example: '2025-01-01'
                                format: date
                              expiry_date:
                                type: string
                                description: 失效日期
                                example: '2025-12-31'
                                format: date
                              rules:
                                type: object
                                description: 政策规则
                                properties:
                                  max_amount:
                                    type: number
                                    description: 最大金额限制
                                    example: 5000.0
                                  booking_advance_days:
                                    type: integer
                                    description: 提前预订天数要求
                                    example: 3
                                  approval_required:
                                    type: boolean
                                    description: 是否需要审批
                                    example: true
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  policies:
                  - policy_id: POL001
                    policy_name: 标准差旅政策
                    policy_type: flight
                    effective_date: '2025-01-01'
                    expiry_date: '2025-12-31'
                    rules:
                      max_amount: 5000.0
                      booking_advance_days: 3
                      approval_required: true
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人有效出差申请单:
    post:
      tags:
      - traveler
      summary: 获取出行人有效出差申请单
      description: 获取出行人有效出差申请单接口，用于相关业务处理
      operationId: api_12_post__api_v1_出行人有效出差申请单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        policies:
                          type: array
                          description: 差旅政策列表
                          example:
                          - policy_id: POL001
                            policy_name: 标准差旅政策
                            policy_type: flight
                            effective_date: '2025-01-01'
                            expiry_date: '2025-12-31'
                            rules:
                              max_amount: 5000.0
                              booking_advance_days: 3
                              approval_required: true
                          items:
                            type: object
                            properties:
                              policy_id:
                                type: string
                                description: 政策ID
                                example: POL001
                              policy_name:
                                type: string
                                description: 政策名称
                                example: 标准差旅政策
                              policy_type:
                                type: string
                                description: 政策类型
                                example: flight
                                enum:
                                - flight
                                - hotel
                                - train
                                - taxi
                                - meal
                              effective_date:
                                type: string
                                description: 生效日期
                                example: '2025-01-01'
                                format: date
                              expiry_date:
                                type: string
                                description: 失效日期
                                example: '2025-12-31'
                                format: date
                              rules:
                                type: object
                                description: 政策规则
                                properties:
                                  max_amount:
                                    type: number
                                    description: 最大金额限制
                                    example: 5000.0
                                  booking_advance_days:
                                    type: integer
                                    description: 提前预订天数要求
                                    example: 3
                                  approval_required:
                                    type: boolean
                                    description: 是否需要审批
                                    example: true
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  policies:
                  - policy_id: POL001
                    policy_name: 标准差旅政策
                    policy_type: flight
                    effective_date: '2025-01-01'
                    expiry_date: '2025-12-31'
                    rules:
                      max_amount: 5000.0
                      booking_advance_days: 3
                      approval_required: true
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人管控政策简单（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人管控政策简单（第一版暂不实施）
      description: 获取出行人管控政策简单（第一版暂不实施）接口，用于相关业务处理
      operationId: api_13_post__api_v1_出行人管控政策简单（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）
      description: 获取出行人管控政策详细-普通差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理
      operationId: api_14_post__api_v1_出行人管控政策详细_普通差旅（申请管控）（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        policies:
                          type: array
                          description: 差旅政策列表
                          example:
                          - policy_id: POL001
                            policy_name: 标准差旅政策
                            policy_type: flight
                            effective_date: '2025-01-01'
                            expiry_date: '2025-12-31'
                            rules:
                              max_amount: 5000.0
                              booking_advance_days: 3
                              approval_required: true
                          items:
                            type: object
                            properties:
                              policy_id:
                                type: string
                                description: 政策ID
                                example: POL001
                              policy_name:
                                type: string
                                description: 政策名称
                                example: 标准差旅政策
                              policy_type:
                                type: string
                                description: 政策类型
                                example: flight
                                enum:
                                - flight
                                - hotel
                                - train
                                - taxi
                                - meal
                              effective_date:
                                type: string
                                description: 生效日期
                                example: '2025-01-01'
                                format: date
                              expiry_date:
                                type: string
                                description: 失效日期
                                example: '2025-12-31'
                                format: date
                              rules:
                                type: object
                                description: 政策规则
                                properties:
                                  max_amount:
                                    type: number
                                    description: 最大金额限制
                                    example: 5000.0
                                  booking_advance_days:
                                    type: integer
                                    description: 提前预订天数要求
                                    example: 3
                                  approval_required:
                                    type: boolean
                                    description: 是否需要审批
                                    example: true
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  policies:
                  - policy_id: POL001
                    policy_name: 标准差旅政策
                    policy_type: flight
                    effective_date: '2025-01-01'
                    expiry_date: '2025-12-31'
                    rules:
                      max_amount: 5000.0
                      booking_advance_days: 3
                      approval_required: true
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）
      description: 获取出行人管控政策详细-福利差旅（申请管控）（第一版暂不实施）接口，用于相关业务处理
      operationId: api_15_post__api_v1_出行人管控政策详细_福利差旅（申请管控）（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        policies:
                          type: array
                          description: 差旅政策列表
                          example:
                          - policy_id: POL001
                            policy_name: 标准差旅政策
                            policy_type: flight
                            effective_date: '2025-01-01'
                            expiry_date: '2025-12-31'
                            rules:
                              max_amount: 5000.0
                              booking_advance_days: 3
                              approval_required: true
                          items:
                            type: object
                            properties:
                              policy_id:
                                type: string
                                description: 政策ID
                                example: POL001
                              policy_name:
                                type: string
                                description: 政策名称
                                example: 标准差旅政策
                              policy_type:
                                type: string
                                description: 政策类型
                                example: flight
                                enum:
                                - flight
                                - hotel
                                - train
                                - taxi
                                - meal
                              effective_date:
                                type: string
                                description: 生效日期
                                example: '2025-01-01'
                                format: date
                              expiry_date:
                                type: string
                                description: 失效日期
                                example: '2025-12-31'
                                format: date
                              rules:
                                type: object
                                description: 政策规则
                                properties:
                                  max_amount:
                                    type: number
                                    description: 最大金额限制
                                    example: 5000.0
                                  booking_advance_days:
                                    type: integer
                                    description: 提前预订天数要求
                                    example: 3
                                  approval_required:
                                    type: boolean
                                    description: 是否需要审批
                                    example: true
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  policies:
                  - policy_id: POL001
                    policy_name: 标准差旅政策
                    policy_type: flight
                    effective_date: '2025-01-01'
                    expiry_date: '2025-12-31'
                    rules:
                      max_amount: 5000.0
                      booking_advance_days: 3
                      approval_required: true
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
              required:
              - employee_id
            example:
              employee_id: EMP001
  /api/v1/create/出差申请单（内）（第一版暂不实施）:
    post:
      tags:
      - employee
      summary: 创建出差申请单（内）（第一版暂不实施）
      description: 创建出差申请单（内）（第一版暂不实施）接口，用于相关业务处理
      operationId: api_16_post__api_v1_create_出差申请单（内）（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /api/v1/create/出差申请单（外）（第一版暂不实施）:
    post:
      tags:
      - employee
      summary: 创建出差申请单（外）（第一版暂不实施）
      description: 创建出差申请单（外）（第一版暂不实施）接口，用于相关业务处理
      operationId: api_17_post__api_v1_create_出差申请单（外）（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                departure:
                  type: string
                  description: 出发地城市或地点
                  example: 北京
                  minLength: 2
                  maxLength: 100
                destination:
                  type: string
                  description: 目的地城市或地点
                  example: 上海
                  minLength: 2
                  maxLength: 100
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
                policy_id:
                  type: string
                  description: 差旅政策ID
                  example: POL001
                  pattern: ^POL[0-9]{3,}$
              required:
              - departure
              - destination
              - order_id
            example:
              departure: 北京
              destination: 上海
              order_id: TO2025073100001
              policy_id: POL001
  /api/v1/出行人当前位置:
    post:
      tags:
      - traveler
      summary: 获取出行人当前位置
      description: 获取出行人当前位置接口，用于相关业务处理
      operationId: api_18_post__api_v1_出行人当前位置
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/员工产品预订权限:
    post:
      tags:
      - employee
      summary: 获取员工产品预订权限
      description: 获取员工产品预订权限接口，用于相关业务处理
      operationId: api_19_post__api_v1_员工产品预订权限
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人未出行订单-航班（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人未出行订单-航班（第一版暂不实施）
      description: 获取出行人未出行订单-航班（第一版暂不实施）接口，用于相关业务处理
      operationId: api_20_post__api_v1_出行人未出行订单_航班（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人未出行订单-火车票（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人未出行订单-火车票（第一版暂不实施）
      description: 获取出行人未出行订单-火车票（第一版暂不实施）接口，用于相关业务处理
      operationId: api_21_post__api_v1_出行人未出行订单_火车票（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人未出行订单-酒店（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人未出行订单-酒店（第一版暂不实施）
      description: 获取出行人未出行订单-酒店（第一版暂不实施）接口，用于相关业务处理
      operationId: api_22_post__api_v1_出行人未出行订单_酒店（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        poi_list:
                          type: array
                          description: POI地址信息列表
                          example:
                          - poi_id: POI001
                            poi_name: 公司总部
                            poi_type: office
                            address: 北京市朝阳区xxx路xxx号
                            city: 北京
                            coordinates:
                              longitude: 116.397128
                              latitude: 39.916527
                              coordinate_system: GCJ02
                          items:
                            type: object
                            properties:
                              poi_id:
                                type: string
                                description: POI唯一标识
                                example: POI001
                              poi_name:
                                type: string
                                description: POI名称
                                example: 公司总部
                              poi_type:
                                type: string
                                description: POI类型
                                example: office
                                enum:
                                - office
                                - hotel
                                - airport
                                - station
                                - other
                              address:
                                type: string
                                description: 详细地址
                                example: 北京市朝阳区xxx路xxx号
                              city:
                                type: string
                                description: 所在城市
                                example: 北京
                              coordinates:
                                type: object
                                description: 坐标信息
                                properties:
                                  longitude:
                                    type: number
                                    description: 经度
                                    example: 116.397128
                                  latitude:
                                    type: number
                                    description: 纬度
                                    example: 39.916527
                                  coordinate_system:
                                    type: string
                                    description: 坐标系统
                                    example: GCJ02
                                    enum:
                                    - WGS84
                                    - GCJ02
                                    - BD09
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  poi_list:
                  - poi_id: POI001
                    poi_name: 公司总部
                    poi_type: office
                    address: 北京市朝阳区xxx路xxx号
                    city: 北京
                    coordinates:
                      longitude: 116.397128
                      latitude: 39.916527
                      coordinate_system: GCJ02
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人未出行订单-用车（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人未出行订单-用车（第一版暂不实施）
      description: 获取出行人未出行订单-用车（第一版暂不实施）接口，用于相关业务处理
      operationId: api_23_post__api_v1_出行人未出行订单_用车（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人未使用机票（open票）:
    post:
      tags:
      - traveler
      summary: 获取出行人未使用机票（OPEN票）
      description: 获取出行人未使用机票（OPEN票）接口，用于相关业务处理
      operationId: api_24_post__api_v1_出行人未使用机票（open票）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人出发地用车与目的地用车历史订单:
    post:
      tags:
      - traveler
      summary: 获取出行人出发地用车与目的地用车历史订单
      description: 获取出行人出发地用车与目的地用车历史订单接口，用于相关业务处理
      operationId: api_25_post__api_v1_出行人出发地用车与目的地用车历史订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人所属公司授信/预存剩余额度是否>0:
    post:
      tags:
      - traveler
      summary: 获取出行人所属公司授信/预存剩余额度是否>0
      description: 获取出行人所属公司授信/预存剩余额度是否>0接口，用于相关业务处理
      operationId: api_26_post__api_v1_出行人所属公司授信_预存剩余额度是否>0
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /api/v1/行程方案中地点与时间跨度的天气:
    post:
      tags:
      - employee
      summary: 获取行程方案中地点与时间跨度的天气
      description: 获取行程方案中地点与时间跨度的天气接口，用于相关业务处理
      operationId: api_27_post__api_v1_行程方案中地点与时间跨度的天气
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /api/v1/出行人历史相同行程订单-火车票:
    post:
      tags:
      - traveler
      summary: 获取出行人历史相同行程订单-火车票
      description: 获取出行人历史相同行程订单-火车票接口，用于相关业务处理
      operationId: api_28_post__api_v1_出行人历史相同行程订单_火车票
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人历史相同行程订单-机票:
    post:
      tags:
      - traveler
      summary: 获取出行人历史相同行程订单-机票
      description: 获取出行人历史相同行程订单-机票接口，用于相关业务处理
      operationId: api_29_post__api_v1_出行人历史相同行程订单_机票
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）:
    post:
      tags:
      - traveler
      summary: 获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）
      description: 获取出行人或者所属公司历史相同行程订单-用车（第一版暂不实施）接口，用于相关业务处理
      operationId: api_30_post__api_v1_出行人或者所属公司历史相同行程订单_用车（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/航班搜索列表（需要包含舱位与可购保险信息）:
    post:
      tags:
      - employee
      summary: 获取航班搜索列表（需要包含舱位与可购保险信息）
      description: 获取航班搜索列表（需要包含舱位与可购保险信息）接口，用于相关业务处理
      operationId: api_31_post__api_v1_航班搜索列表（需要包含舱位与可购保险信息）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
              page: 1
              page_size: 20
  /api/v1/车次搜索列表（需要包含各座位类型余票与可购保险信息）:
    post:
      tags:
      - employee
      summary: 获取车次搜索列表（需要包含各座位类型余票与可购保险信息）
      description: 获取车次搜索列表（需要包含各座位类型余票与可购保险信息）接口，用于相关业务处理
      operationId: api_32_post__api_v1_车次搜索列表（需要包含各座位类型余票与可购保险信息）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
              page: 1
              page_size: 20
  /api/v1/精选酒店列表:
    post:
      tags:
      - employee
      summary: 获取精选酒店列表
      description: 获取精选酒店列表接口，用于相关业务处理
      operationId: api_33_post__api_v1_精选酒店列表
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
            example:
              employee_id: EMP001
              page: 1
              page_size: 20
  /api/v1/酒店搜索列表:
    post:
      tags:
      - employee
      summary: 获取酒店搜索列表
      description: 获取酒店搜索列表接口，用于相关业务处理
      operationId: api_34_post__api_v1_酒店搜索列表
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
              page: 1
              page_size: 20
  /api/v1/酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）:
    post:
      tags:
      - policy
      summary: 获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）
      description: 获取酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）接口，用于相关业务处理
      operationId: api_35_post__api_v1_酒店基本信息、酒店房型、酒店图片、酒店设施信息（需要一起返回房型政策信息）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        policies:
                          type: array
                          description: 差旅政策列表
                          example:
                          - policy_id: POL001
                            policy_name: 标准差旅政策
                            policy_type: flight
                            effective_date: '2025-01-01'
                            expiry_date: '2025-12-31'
                            rules:
                              max_amount: 5000.0
                              booking_advance_days: 3
                              approval_required: true
                          items:
                            type: object
                            properties:
                              policy_id:
                                type: string
                                description: 政策ID
                                example: POL001
                              policy_name:
                                type: string
                                description: 政策名称
                                example: 标准差旅政策
                              policy_type:
                                type: string
                                description: 政策类型
                                example: flight
                                enum:
                                - flight
                                - hotel
                                - train
                                - taxi
                                - meal
                              effective_date:
                                type: string
                                description: 生效日期
                                example: '2025-01-01'
                                format: date
                              expiry_date:
                                type: string
                                description: 失效日期
                                example: '2025-12-31'
                                format: date
                              rules:
                                type: object
                                description: 政策规则
                                properties:
                                  max_amount:
                                    type: number
                                    description: 最大金额限制
                                    example: 5000.0
                                  booking_advance_days:
                                    type: integer
                                    description: 提前预订天数要求
                                    example: 3
                                  approval_required:
                                    type: boolean
                                    description: 是否需要审批
                                    example: true
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  policies:
                  - policy_id: POL001
                    policy_name: 标准差旅政策
                    policy_type: flight
                    effective_date: '2025-01-01'
                    expiry_date: '2025-12-31'
                    rules:
                      max_amount: 5000.0
                      booking_advance_days: 3
                      approval_required: true
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
            example:
              page: 1
              page_size: 20
  /api/v1/用车列表（第一版暂不实施）:
    post:
      tags:
      - employee
      summary: 获取用车列表（第一版暂不实施）
      description: 获取用车列表（第一版暂不实施）接口，用于相关业务处理
      operationId: api_36_post__api_v1_用车列表（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
                departure:
                  type: string
                  description: 出发地城市或地点
                  example: 北京
                  minLength: 2
                  maxLength: 100
                departure_time:
                  type: string
                  description: 计划出发时间
                  example: '2025-08-15 09:00:00'
                  pattern: ^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$
                  format: datetime
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
              required:
              - employee_id
              - departure
              - departure_time
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
              departure: 北京
              departure_time: '2025-08-15 09:00:00'
              page: 1
              page_size: 20
  /api/v1/出行人出差申请单状态:
    post:
      tags:
      - traveler
      summary: 获取出行人出差申请单状态
      description: 获取出行人出差申请单状态接口，用于相关业务处理
      operationId: api_37_post__api_v1_出行人出差申请单状态
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/出行人行程提交项启用状态:
    post:
      tags:
      - traveler
      summary: 获取出行人行程提交项启用状态
      description: 获取出行人行程提交项启用状态接口，用于相关业务处理
      operationId: api_38_post__api_v1_出行人行程提交项启用状态
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                employee_id:
                  type: string
                  description: 员工唯一标识符
                  example: EMP001
                  pattern: ^EMP[0-9]{3,}$
                  minLength: 6
                  maxLength: 20
                traveler_employee_ids:
                  type: array
                  description: 出行人员工ID列表，支持多个出行人
                  example:
                  - EMP001
                  - EMP002
                  items:
                    type: string
                    pattern: ^EMP[0-9]{3,}$
              required:
              - employee_id
            example:
              employee_id: EMP001
              traveler_employee_ids:
              - EMP001
              - EMP002
  /api/v1/结算金额信息:
    post:
      tags:
      - employee
      summary: 获取结算金额信息
      description: 获取结算金额信息接口，用于相关业务处理
      operationId: api_39_post__api_v1_结算金额信息
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      - $ref: '#/components/parameters/PageParam'
      - $ref: '#/components/parameters/PageSizeParam'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        pagination:
                          type: object
                          description: 分页信息
                          example:
                            current_page: 1
                            page_size: 20
                            total_count: 150
                            total_pages: 8
                            has_next: true
                            has_prev: false
                          properties:
                            current_page:
                              type: integer
                              description: 当前页码
                              example: 1
                            page_size:
                              type: integer
                              description: 每页数量
                              example: 20
                            total_count:
                              type: integer
                              description: 总记录数
                              example: 150
                            total_pages:
                              type: integer
                              description: 总页数
                              example: 8
                            has_next:
                              type: boolean
                              description: 是否有下一页
                              example: true
                            has_prev:
                              type: boolean
                              description: 是否有上一页
                              example: false
              example:
                code: 200
                message: success
                data:
                  pagination:
                    current_page: 1
                    page_size: 20
                    total_count: 150
                    total_pages: 8
                    has_next: true
                    has_prev: false
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  description: 页码，从1开始
                  example: 1
                  minimum: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 20
                  minimum: 1
                  maximum: 100
            example:
              page: 1
              page_size: 20
  /api/v1/提交订单（支持多品类多订单）:
    post:
      tags:
      - employee
      summary: 提交订单（支持多品类多订单）
      description: 提交订单（支持多品类多订单）接口，用于相关业务处理
      operationId: api_40_post__api_v1_提交订单（支持多品类多订单）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: string
                  description: 差旅政策ID
                  example: POL001
                  pattern: ^POL[0-9]{3,}$
            example:
              policy_id: POL001
  /api/v1/申请改签机票订单:
    post:
      tags:
      - employee
      summary: 申请改签机票订单
      description: 申请改签机票订单接口，用于相关业务处理
      operationId: api_41_post__api_v1_申请改签机票订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/确认改签机票订单:
    post:
      tags:
      - employee
      summary: 确认改签机票订单
      description: 确认改签机票订单接口，用于相关业务处理
      operationId: api_42_post__api_v1_确认改签机票订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/变更酒店订单:
    post:
      tags:
      - employee
      summary: 变更酒店订单
      description: 变更酒店订单接口，用于相关业务处理
      operationId: api_43_post__api_v1_变更酒店订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/验证是否可改火车票订单:
    post:
      tags:
      - employee
      summary: 验证是否可改火车票订单
      description: 验证是否可改火车票订单接口，用于相关业务处理
      operationId: api_44_post__api_v1_验证是否可改火车票订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/申请改签火车票订单:
    post:
      tags:
      - employee
      summary: 申请改签火车票订单
      description: 申请改签火车票订单接口，用于相关业务处理
      operationId: api_45_post__api_v1_申请改签火车票订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/确认改签火车票订单:
    post:
      tags:
      - employee
      summary: 确认改签火车票订单
      description: 确认改签火车票订单接口，用于相关业务处理
      operationId: api_46_post__api_v1_确认改签火车票订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/12306账号登录:
    post:
      tags:
      - authentication
      summary: 12306账号登录
      description: 12306账号登录接口，用于相关业务处理
      operationId: api_47_post__api_v1_12306账号登录
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/12306常旅客信息查询:
    post:
      tags:
      - employee
      summary: 12306常旅客信息查询
      description: 12306常旅客信息查询接口，用于相关业务处理
      operationId: api_48_post__api_v1_12306常旅客信息查询
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        employee_name:
                          type: string
                          description: 员工姓名
                          example: 张三
                          minLength: 2
                          maxLength: 50
                        documents:
                          type: array
                          description: 证件信息列表
                          example:
                          - document_type: 身份证
                            document_number: '110101199001011234'
                            expiry_date: '2030-12-31'
                            issuing_authority: 北京市公安局
                          items:
                            type: object
                            properties:
                              document_type:
                                type: string
                                description: 证件类型
                                example: 身份证
                                enum:
                                - 身份证
                                - 护照
                                - 港澳通行证
                                - 台湾通行证
                                - 驾驶证
                              document_number:
                                type: string
                                description: 证件号码
                                example: '110101199001011234'
                              expiry_date:
                                type: string
                                description: 有效期至
                                example: '2030-12-31'
                                format: date
                              issuing_authority:
                                type: string
                                description: 发证机关
                                example: 北京市公安局
              example:
                code: 200
                message: success
                data:
                  employee_name: 张三
                  documents:
                  - document_type: 身份证
                    document_number: '110101199001011234'
                    expiry_date: '2030-12-31'
                    issuing_authority: 北京市公安局
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /api/v1/提交用车订单（第一版暂不实施）:
    post:
      tags:
      - employee
      summary: 提交用车订单（第一版暂不实施）
      description: 提交用车订单（第一版暂不实施）接口，用于相关业务处理
      operationId: api_49_post__api_v1_提交用车订单（第一版暂不实施）
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /api/v1/提交火车票出票申请:
    post:
      tags:
      - employee
      summary: 提交火车票出票申请
      description: 提交火车票出票申请接口，用于相关业务处理
      operationId: api_50_post__api_v1_提交火车票出票申请
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/提交机票出票申请:
    post:
      tags:
      - employee
      summary: 提交机票出票申请
      description: 提交机票出票申请接口，用于相关业务处理
      operationId: api_51_post__api_v1_提交机票出票申请
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
              example:
                code: 200
                message: success
                data: {}
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: 差旅单唯一标识
                  example: TO2025073100001
                  pattern: ^TO\d{13}$
              required:
              - order_id
            example:
              order_id: TO2025073100001
  /api/v1/提交保险订单:
    post:
      tags:
      - employee
      summary: 提交保险订单
      description: 提交保险订单接口，用于相关业务处理
      operationId: api_52_post__api_v1_提交保险订单
      parameters:
      - $ref: '#/components/parameters/EnterpriseIdHeader'
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/SuccessResponse'
                - properties:
                    data:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: 差旅单唯一标识
                          example: TO2025073100001
                          pattern: ^TO\d{13}$
                        order_status:
                          type: string
                          description: 差旅单状态
                          example: submitted
                          enum:
                          - draft
                          - submitted
                          - approved
                          - rejected
                          - cancelled
                          - completed
                        create_time:
                          type: string
                          description: 创建时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
                        update_time:
                          type: string
                          description: 更新时间
                          example: '2025-07-31 14:30:25'
                          format: datetime
              example:
                code: 200
                message: success
                data:
                  order_id: TO2025073100001
                  order_status: submitted
                  create_time: '2025-07-31 14:30:25'
                  update_time: '2025-07-31 14:30:25'
                timestamp: '2025-07-31 14:30:25'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: success
        data:
          description: 响应数据
          oneOf:
          - type: object
          - type: array
          - type: 'null'
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
          example: '2025-07-31T14:30:25+08:00'
      required:
      - code
      - message
      - data
      - timestamp
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          enum:
          - 400
          - 401
          - 403
          - 404
          - 500
        message:
          type: string
          description: 错误消息
        data:
          type: 'null'
          description: 错误时数据为空
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
      required:
      - code
      - message
      - data
      - timestamp
    Pagination:
      type: object
      properties:
        current_page:
          type: integer
          description: 当前页码
          example: 1
        page_size:
          type: integer
          description: 每页数量
          example: 20
        total_count:
          type: integer
          description: 总记录数
          example: 150
        total_pages:
          type: integer
          description: 总页数
          example: 8
        has_next:
          type: boolean
          description: 是否有下一页
          example: true
        has_prev:
          type: boolean
          description: 是否有上一页
          example: false
      required:
      - current_page
      - page_size
      - total_count
      - total_pages
      - has_next
      - has_prev
    Organization:
      type: object
      properties:
        company_id:
          type: string
          description: 公司ID
          example: COMP001
        company_name:
          type: string
          description: 公司名称
          example: 科技有限公司
        department_id:
          type: string
          description: 部门ID
          example: DEPT001
        department_name:
          type: string
          description: 部门名称
          example: 技术部
        team_id:
          type: string
          description: 团队ID
          example: TEAM001
        team_name:
          type: string
          description: 团队名称
          example: 后端开发组
        job_title:
          type: string
          description: 职位名称
          example: 高级工程师
        job_level:
          type: string
          description: 职级
          example: P7
        manager_id:
          type: string
          description: 直属领导员工ID
          example: EMP100
        cost_center:
          type: string
          description: 成本中心
          example: CC001
    Contact:
      type: object
      properties:
        mobile:
          type: string
          description: 手机号码
          pattern: ^1[3-9]\d{9}$
          example: '13800138000'
        email:
          type: string
          format: email
          description: 邮箱地址
          example: <EMAIL>
        work_phone:
          type: string
          description: 工作电话
          example: 010-12345678
        emergency_contact:
          $ref: '#/components/schemas/EmergencyContact'
    EmergencyContact:
      type: object
      properties:
        name:
          type: string
          description: 紧急联系人姓名
          example: 李四
        phone:
          type: string
          description: 紧急联系人电话
          example: '13900139000'
        relationship:
          type: string
          description: 关系
          example: 配偶
    Document:
      type: object
      properties:
        document_type:
          type: string
          description: 证件类型
          enum:
          - 身份证
          - 护照
          - 港澳通行证
          - 台湾通行证
          - 驾驶证
          example: 身份证
        document_number:
          type: string
          description: 证件号码
          example: '110101199001011234'
        expiry_date:
          type: string
          format: date
          description: 有效期至
          example: '2030-12-31'
        issuing_authority:
          type: string
          description: 发证机关
          example: 北京市公安局
    Card:
      type: object
      properties:
        card_id:
          type: string
          description: 卡片ID
          example: CARD001
        card_type:
          type: string
          description: 卡片类型
          enum:
          - 信用卡
          - 借记卡
          - 企业卡
          - 预付卡
          example: 信用卡
        card_number:
          type: string
          description: 卡号（脱敏显示）
          pattern: \*{4}\d{4}
          example: '****1234'
        card_holder:
          type: string
          description: 持卡人姓名
          example: 张三
        issuing_bank:
          type: string
          description: 发卡银行
          example: 招商银行
        is_default:
          type: boolean
          description: 是否为默认卡片
          example: true
        is_corporate:
          type: boolean
          description: 是否为企业卡
          example: false
        status:
          type: string
          description: 卡片状态
          enum:
          - active
          - inactive
          - expired
          example: active
    POI:
      type: object
      properties:
        poi_id:
          type: string
          description: POI唯一标识
          example: POI001
        poi_name:
          type: string
          description: POI名称
          example: 公司总部
        poi_type:
          type: string
          description: POI类型
          enum:
          - office
          - hotel
          - airport
          - station
          - other
          example: office
        address:
          type: string
          description: 详细地址
          example: 北京市朝阳区xxx路xxx号
        city:
          type: string
          description: 所在城市
          example: 北京
        coordinates:
          $ref: '#/components/schemas/Coordinates'
    Coordinates:
      type: object
      properties:
        longitude:
          type: number
          description: 经度
          example: 116.397128
        latitude:
          type: number
          description: 纬度
          example: 39.916527
        coordinate_system:
          type: string
          description: 坐标系统
          enum:
          - WGS84
          - GCJ02
          - BD09
          example: GCJ02
    Policy:
      type: object
      properties:
        policy_id:
          type: string
          description: 政策ID
          example: POL001
        policy_name:
          type: string
          description: 政策名称
          example: 标准差旅政策
        policy_type:
          type: string
          description: 政策类型
          enum:
          - flight
          - hotel
          - train
          - taxi
          - meal
          example: flight
        effective_date:
          type: string
          format: date
          description: 生效日期
          example: '2025-01-01'
        expiry_date:
          type: string
          format: date
          description: 失效日期
          example: '2025-12-31'
        rules:
          $ref: '#/components/schemas/PolicyRules'
    PolicyRules:
      type: object
      properties:
        max_amount:
          type: number
          description: 最大金额限制
          example: 5000.0
        booking_advance_days:
          type: integer
          description: 提前预订天数要求
          example: 3
        approval_required:
          type: boolean
          description: 是否需要审批
          example: true
  responses:
    UnauthorizedError:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    BadRequestError:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  parameters:
    EnterpriseIdHeader:
      name: X-Enterprise-ID
      in: header
      required: true
      description: 企业唯一标识
      schema:
        type: string
        pattern: ^ENT[0-9]{3,}$
        example: ENT001
    PageParam:
      name: page
      in: query
      description: 页码，从1开始
      required: false
      schema:
        type: integer
        minimum: 1
        default: 1
        example: 1
    PageSizeParam:
      name: page_size
      in: query
      description: 每页数量
      required: false
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
        example: 20
