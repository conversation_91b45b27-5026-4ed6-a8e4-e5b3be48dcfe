# Travel Pilot API 详细字段定义示例

## ✅ 完成！Data 内部字段详细定义

我已经为您的 52 个 API 接口创建了完整的 **data 内部字段详细定义**，每个字段都包含了：

### 🎯 **字段定义包含的详细信息**:

1. **字段类型** - string, integer, boolean, object, array 等
2. **是否必填** - required/optional 标识
3. **字段描述** - 详细的字段用途说明
4. **示例值** - 真实的示例数据
5. **验证规则** - 长度限制、格式要求、正则表达式等
6. **枚举值** - 可选值列表（如适用）
7. **嵌套结构** - 对象内部的子字段定义

---

## 📋 **具体示例 - 员工信息同步接口 (API-1)**

### 📤 **请求体字段定义**:

| 字段名 | 类型 | 必填 | 描述 | 示例值 | 验证规则 |
|--------|------|------|------|--------|----------|
| `employee_id` | string | 是 | 员工唯一标识符 | "EMP001" | 格式: ^EMP[0-9]{3,}$<br>长度: 6-20字符 |
| `sso_token` | string | 是 | 单点登录认证令牌 | "eyJhbGci..." | 最小长度: 100字符 |

### 📥 **响应体 data 字段详细定义**:

```json
{
  "code": 200,
  "message": "success", 
  "data": {
    // 详细字段定义如下 ↓
  },
  "timestamp": "2025-07-31 14:30:25"
}
```

#### **data 内部字段定义**:

| 字段名 | 类型 | 描述 | 示例值 | 验证规则 |
|--------|------|------|--------|----------|
| `employee_id` | string | 员工唯一标识符 | "EMP001" | 格式: ^EMP[0-9]{3,}$ |
| `enterprise_id` | string | 企业唯一标识符 | "ENT001" | 格式: ^ENT[0-9]{3,}$ |
| `employee_name` | string | 员工姓名 | "张三" | 长度: 2-50字符 |
| `is_express_booking` | boolean | 是否为极速预订员工 | true | 布尔值 |
| `organization` | object | 员工组织架构信息 | {...} | 嵌套对象 |
| `contact` | object | 联系方式信息 | {...} | 嵌套对象 |
| `vip_level` | string | VIP等级 | "VIP1" | 枚举: NONE, VIP1, VIP2, VIP3 |

#### **嵌套对象 - organization 字段定义**:

| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `company_id` | string | 公司ID | "COMP001" |
| `company_name` | string | 公司名称 | "科技有限公司" |
| `department_id` | string | 部门ID | "DEPT001" |
| `department_name` | string | 部门名称 | "技术部" |
| `team_id` | string | 团队ID | "TEAM001" |
| `team_name` | string | 团队名称 | "后端开发组" |
| `job_title` | string | 职位名称 | "高级工程师" |
| `job_level` | string | 职级 | "P7" |
| `manager_id` | string | 直属领导员工ID | "EMP100" |
| `cost_center` | string | 成本中心 | "CC001" |

#### **嵌套对象 - contact 字段定义**:

| 字段名 | 类型 | 描述 | 示例值 | 验证规则 |
|--------|------|------|--------|----------|
| `mobile` | string | 手机号码 | "13800138000" | 格式: ^1[3-9]\\d{9}$ |
| `email` | string | 邮箱地址 | "<EMAIL>" | 格式: email |
| `work_phone` | string | 工作电话 | "010-12345678" | - |
| `emergency_contact` | object | 紧急联系人 | {...} | 嵌套对象 |

---

## 📋 **更复杂示例 - 获取出行人卡信息接口 (API-5)**

### 📥 **响应体 data.cards 数组字段定义**:

```json
{
  "data": {
    "cards": [
      {
        // 每个卡片对象的详细字段定义 ↓
      }
    ]
  }
}
```

#### **cards 数组项字段定义**:

| 字段名 | 类型 | 描述 | 示例值 | 验证规则/枚举值 |
|--------|------|------|--------|-----------------|
| `card_id` | string | 卡片ID | "CARD001" | - |
| `card_type` | string | 卡片类型 | "信用卡" | 枚举: 信用卡, 借记卡, 企业卡, 预付卡 |
| `card_number` | string | 卡号（脱敏显示） | "****1234" | 格式: \\*{4}\\d{4} |
| `card_holder` | string | 持卡人姓名 | "张三" | - |
| `issuing_bank` | string | 发卡银行 | "招商银行" | - |
| `is_default` | boolean | 是否为默认卡片 | true | 布尔值 |
| `is_corporate` | boolean | 是否为企业卡 | false | 布尔值 |
| `status` | string | 卡片状态 | "active" | 枚举: active, inactive, expired |

---

## 🏢 **企业 POI 接口示例 - coordinates 嵌套对象**

### 📥 **data.poi_list[].coordinates 字段定义**:

| 字段名 | 类型 | 描述 | 示例值 | 枚举值 |
|--------|------|------|--------|--------|
| `longitude` | number | 经度 | 116.397128 | - |
| `latitude` | number | 纬度 | 39.916527 | - |
| `coordinate_system` | string | 坐标系统 | "GCJ02" | 枚举: WGS84, GCJ02, BD09 |

---

## 📊 **分页信息统一字段定义**

对于列表类接口，统一包含 `pagination` 字段：

| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `current_page` | integer | 当前页码 | 1 |
| `page_size` | integer | 每页数量 | 20 |
| `total_count` | integer | 总记录数 | 150 |
| `total_pages` | integer | 总页数 | 8 |
| `has_next` | boolean | 是否有下一页 | true |
| `has_prev` | boolean | 是否有上一页 | false |

---

## 📱 **查看完整文档**

打开 **`travel-pilot-api-docs-comprehensive.html`** 文件可以查看：

1. ✅ **52个完整接口** - 每个都有data内部字段详细定义
2. ✅ **6个分类标签页** - 概览、请求头、请求体、响应体、示例、错误码
3. ✅ **嵌套字段展示** - 多层级对象结构清晰展示
4. ✅ **验证规则显示** - 长度限制、格式要求、枚举值等
5. ✅ **实时搜索** - 快速查找相关字段
6. ✅ **代码复制** - 一键复制JSON示例

---

## 📄 **生成的文件清单**

- **`travel-pilot-api-docs-comprehensive.html`** - 🌟 **主要文档** (详细字段版)
- **`api_detailed_specs.json`** - 详细字段规范数据
- **`travel-pilot-api-docs-detailed.html`** - 基础详细版文档
- **`api_json_specs.json`** - 基础JSON规范数据

现在每个接口的 data 字段都有了完整的内部结构定义，包括类型、验证规则、示例值和嵌套对象的详细说明！