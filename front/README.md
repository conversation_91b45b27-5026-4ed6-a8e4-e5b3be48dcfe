# 同程智能差旅助手 - 聊天界面

一个现代化的聊天界面，专为同程智能差旅助手设计，提供直观的用户交互体验。

## 🎯 功能特性

### 核心功能
- ✅ **智能对话** - 模拟真实的AI助手对话体验
- ✅ **选项交互** - 支持快速选择预订类型（本人/代他人）
- ✅ **实时输入** - 支持文字输入和语音输入切换
- ✅ **消息历史** - 完整的对话记录和历史管理
- ✅ **打字指示器** - 模拟真实的AI思考过程
- ✅ **响应式设计** - 适配移动端和桌面端

### 界面特色
- 🎨 **现代化UI** - 参考微信聊天界面设计
- 🌈 **渐变主题** - 蓝色渐变配色方案
- ⚡ **流畅动画** - 消息发送、选项选择等动画效果
- 📱 **移动优先** - 专为移动设备优化的交互体验

## 📁 文件结构

```
front/
├── index.html          # 主页面（推荐使用）
├── chatbox.html        # 完整版页面（包含内联样式和脚本）
├── chatbox.css         # 样式文件
├── chatbox.js          # 交互逻辑
└── README.md           # 说明文档
```

## 🚀 快速开始

### 方式一：直接打开（推荐）
```bash
# 进入front目录
cd front

# 使用浏览器打开
open index.html
# 或者
python3 -m http.server 8000
# 然后访问 http://localhost:8000
```

### 方式二：集成到现有项目
```html
<!-- 在你的HTML中引入样式和脚本 -->
<link rel="stylesheet" href="path/to/chatbox.css">
<script src="path/to/chatbox.js"></script>
```

## 🎮 使用说明

### 基本操作
1. **选择预订类型** - 点击"本人预订"或"代他人预订"
2. **发送消息** - 在输入框中输入文字，点击发送按钮
3. **语音输入** - 点击麦克风图标切换语音模式
4. **重新开始** - 点击刷新按钮清空对话历史

### 快捷键
- `Enter` - 发送消息
- `Escape` - 清空输入框

### 界面元素
- **返回按钮** - 退出聊天界面
- **云同步图标** - 同步聊天记录（待实现）
- **菜单图标** - 更多设置选项（待实现）

## 🔧 自定义配置

### 修改主题颜色
在 `chatbox.css` 中修改以下变量：
```css
/* 主色调 */
.header {
    background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
}

/* 用户消息气泡 */
.message-bubble.user .message-content {
    background: #4285f4;
}
```

### 自定义回复内容
在 `chatbox.js` 中修改 `generateResponse()` 方法：
```javascript
const responses = {
    greeting: [
        '您的自定义欢迎消息',
        // 添加更多回复...
    ],
    // 其他类型的回复...
};
```

## 🔌 API 集成

### 连接后端服务
修改 `chatbox.js` 中的 `sendMessage()` 方法：
```javascript
async sendMessage() {
    const message = this.messageInput.value.trim();
    if (message && !this.isTyping) {
        this.addMessage('user', message);
        
        try {
            // 发送到后端API
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    option: this.selectedOption,
                    history: this.chatHistory
                })
            });
            
            const data = await response.json();
            this.addMessage('assistant', data.reply);
        } catch (error) {
            console.error('API Error:', error);
            this.addMessage('assistant', '抱歉，服务暂时不可用，请稍后再试。');
        }
    }
}
```

### 事件监听
```javascript
// 监听消息发送事件
window.chatBox.addEventListener('messageSent', function(event) {
    console.log('Message sent:', event.detail);
});

// 监听选项选择事件
window.chatBox.addEventListener('optionSelected', function(event) {
    console.log('Option selected:', event.detail);
});
```

## 🎨 样式定制

### 响应式断点
```css
/* 手机端 */
@media (max-width: 480px) {
    .chat-container {
        max-width: 100%;
    }
}

/* 平板端 */
@media (min-width: 481px) and (max-width: 768px) {
    .chat-container {
        max-width: 600px;
    }
}
```

### 动画效果
所有动画都使用CSS3，可以通过修改以下类来自定义：
- `.message-bubble` - 消息动画
- `.option-btn` - 选项按钮动画
- `.typing-indicator` - 打字指示器动画

## 🐛 常见问题

### Q: 消息发送后没有回复？
A: 检查浏览器控制台是否有错误，确保 `chatbox.js` 正确加载。

### Q: 样式显示不正常？
A: 确保 `chatbox.css` 文件路径正确，检查浏览器是否支持CSS3。

### Q: 移动端显示异常？
A: 确保HTML中包含了viewport meta标签：
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0">
```

## 📱 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ⚠️ IE 11（部分功能受限）

## 🔄 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础聊天功能
- 选项交互
- 响应式设计

## 📄 许可证

MIT License - 详见项目根目录的 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**开发团队**: Travel Pilot 前端团队  
**联系方式**: <EMAIL>
