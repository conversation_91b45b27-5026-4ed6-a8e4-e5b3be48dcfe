/* 同程智能差旅助手 - 聊天界面样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 400px;
    margin: 0 auto;
    background: #f5f5f5;
    position: relative;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-left {
    display: flex;
    align-items: center;
}

.back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    margin-right: 15px;
    cursor: pointer;
    padding: 5px;
    transition: opacity 0.3s ease;
}

.back-btn:hover {
    opacity: 0.8;
}

.header-title {
    font-size: 18px;
    font-weight: 500;
}

.header-right {
    display: flex;
    gap: 15px;
}

.header-icon {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    transition: opacity 0.3s ease;
}

.header-icon:hover {
    opacity: 0.8;
}

/* Chat Content Styles */
.chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f0f2f5;
}

.assistant-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.assistant-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.assistant-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin-right: 15px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.assistant-info h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.assistant-info p {
    font-size: 14px;
    color: #666;
}

.welcome-message {
    font-size: 15px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 20px;
}

/* Message Styles */
.message-bubble {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble.user {
    justify-content: flex-end;
}

.message-bubble.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 15px;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-bubble.user .message-content {
    background: #4285f4;
    color: white;
    border-bottom-right-radius: 6px;
}

.message-bubble.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 6px;
}

.message-time {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    text-align: center;
}

/* Typing Indicator */
.typing-indicator {
    display: none;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    max-width: 80%;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { 
        transform: scale(0.8); 
        opacity: 0.5; 
    }
    40% { 
        transform: scale(1); 
        opacity: 1; 
    }
}

/* Option Buttons */
.option-buttons {
    display: flex;
    gap: 15px;
    transition: all 0.3s ease;
}

.option-btn {
    flex: 1;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.option-btn:hover {
    border-color: #4285f4;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
}

.option-btn.selected {
    border-color: #4285f4;
    background: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
}

.option-btn .icon {
    width: 40px;
    height: 40px;
    background: #f0f2f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 20px;
    color: #666;
    transition: all 0.3s ease;
}

.option-btn.selected .icon {
    background: #4285f4;
    color: white;
}

.option-btn .title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.option-btn .desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.check-mark {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: #4caf50;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    animation: checkmarkPop 0.3s ease-out;
}

@keyframes checkmarkPop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.option-btn.selected .check-mark {
    display: flex;
}

/* Input Area */
.input-area {
    background: white;
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.refresh-btn, .voice-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    transition: all 0.3s ease;
}

.voice-btn {
    color: #4285f4;
}

.refresh-btn:hover, .voice-btn:hover {
    transform: scale(1.1);
}

.input-wrapper {
    flex: 1;
    position: relative;
}

.message-input {
    width: 100%;
    border: none;
    outline: none;
    font-size: 16px;
    color: #333;
    background: transparent;
}

.message-input::placeholder {
    color: #999;
}

.send-btn {
    background: #4285f4;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover {
    background: #3367d6;
    transform: scale(1.05);
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 480px) {
    .chat-container {
        max-width: 100%;
    }
    
    .option-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .option-btn {
        padding: 15px;
    }
    
    .header {
        padding: 12px 15px;
    }
    
    .chat-content {
        padding: 15px;
    }
}
