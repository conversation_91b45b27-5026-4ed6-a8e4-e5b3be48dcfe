<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同程智能差旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 400px;
            margin: 0 auto;
            background: #f5f5f5;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            margin-right: 15px;
            cursor: pointer;
            padding: 5px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 500;
        }

        .header-right {
            display: flex;
            gap: 15px;
        }

        .header-icon {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .chat-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f0f2f5;
        }

        .assistant-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .message-bubble {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message-bubble.user {
            justify-content: flex-end;
        }

        .message-bubble.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 15px;
            line-height: 1.4;
        }

        .message-bubble.user .message-content {
            background: #4285f4;
            color: white;
            border-bottom-right-radius: 6px;
        }

        .message-bubble.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 6px;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
            text-align: center;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 18px;
            border-bottom-left-radius: 6px;
            max-width: 80%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .assistant-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .assistant-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 15px;
        }

        .assistant-info h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .assistant-info p {
            font-size: 14px;
            color: #666;
        }

        .welcome-message {
            font-size: 15px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 20px;
        }

        .option-buttons {
            display: flex;
            gap: 15px;
        }

        .option-btn {
            flex: 1;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .option-btn.selected {
            border-color: #4285f4;
            background: #f8f9ff;
        }

        .option-btn .icon {
            width: 40px;
            height: 40px;
            background: #f0f2f5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 20px;
            color: #666;
        }

        .option-btn.selected .icon {
            background: #4285f4;
            color: white;
        }

        .option-btn .title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .option-btn .desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .check-mark {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            background: #4caf50;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .option-btn.selected .check-mark {
            display: flex;
        }

        .input-area {
            background: white;
            padding: 15px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .refresh-btn {
            background: none;
            border: none;
            color: #666;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        .voice-btn {
            background: none;
            border: none;
            color: #4285f4;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            border: none;
            outline: none;
            font-size: 16px;
            color: #333;
            background: transparent;
        }

        .message-input::placeholder {
            color: #999;
        }

        .send-btn {
            background: #4285f4;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .send-btn:hover {
            background: #3367d6;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        @media (max-width: 480px) {
            .chat-container {
                max-width: 100%;
            }
            
            .option-buttons {
                flex-direction: column;
                gap: 10px;
            }
            
            .option-btn {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <button class="back-btn">←</button>
                <span class="header-title">同程智能差旅助手</span>
            </div>
            <div class="header-right">
                <button class="header-icon">☁</button>
                <button class="header-icon">☰</button>
            </div>
        </div>

        <!-- Chat Content -->
        <div class="chat-content" id="chatContent">
            <div class="assistant-card">
                <div class="assistant-header">
                    <div class="assistant-avatar">🤖</div>
                    <div class="assistant-info">
                        <h3>智能差旅助手</h3>
                        <p>专业出行规划</p>
                    </div>
                </div>

                <div class="welcome-message">
                    您好！欢迎使用智能差旅助手。请问您是为自己预订行程，还是为他人代订？
                </div>

                <div class="option-buttons" id="optionButtons">
                    <div class="option-btn selected" data-option="self">
                        <div class="check-mark">✓</div>
                        <div class="icon">👤</div>
                        <div class="title">本人预订</div>
                        <div class="desc">为自己预订行程，可使用个人差旅单</div>
                    </div>

                    <div class="option-btn" data-option="others">
                        <div class="check-mark">✓</div>
                        <div class="icon">👥</div>
                        <div class="title">代他人预订</div>
                        <div class="desc">为同事或团队预订行程</div>
                    </div>
                </div>
            </div>

            <!-- Messages will be dynamically added here -->
            <div id="messagesContainer"></div>

            <!-- Typing indicator -->
            <div class="message-bubble assistant">
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <button class="refresh-btn">🔄</button>
            <button class="voice-btn">🎤</button>
            <div class="input-wrapper">
                <input type="text" class="message-input" placeholder="输入您的需求，如"帮我预订经济实惠的"">
            </div>
            <button class="send-btn" disabled>➤</button>
        </div>
    </div>

    <script>
        // Chat state
        let selectedOption = 'self';
        let chatHistory = [];

        // DOM elements
        const messageInput = document.querySelector('.message-input');
        const sendBtn = document.querySelector('.send-btn');
        const chatContent = document.querySelector('#chatContent');
        const messagesContainer = document.querySelector('#messagesContainer');
        const typingIndicator = document.querySelector('#typingIndicator');
        const optionButtons = document.querySelector('#optionButtons');

        // Option selection functionality
        document.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove selected class from all buttons
                document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
                // Add selected class to clicked button
                this.classList.add('selected');
                selectedOption = this.dataset.option;

                // Send option selection as message
                const optionText = selectedOption === 'self' ? '本人预订' : '代他人预订';
                addMessage('user', `我选择：${optionText}`);

                // Hide option buttons after selection
                setTimeout(() => {
                    optionButtons.style.display = 'none';
                    simulateAssistantResponse();
                }, 500);
            });
        });

        // Input functionality
        messageInput.addEventListener('input', function() {
            sendBtn.disabled = this.value.trim() === '';
        });

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !sendBtn.disabled) {
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                addMessage('user', message);
                messageInput.value = '';
                sendBtn.disabled = true;

                // Simulate assistant response
                setTimeout(() => {
                    simulateAssistantResponse();
                }, 1000);
            }
        }

        function addMessage(sender, text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-bubble ${sender}`;

            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                <div class="message-content">${text}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            chatHistory.push({ sender, text, time: timeStr });

            // Scroll to bottom
            chatContent.scrollTop = chatContent.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'flex';
            chatContent.scrollTop = chatContent.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        function simulateAssistantResponse() {
            showTyping();

            setTimeout(() => {
                hideTyping();

                // Generate response based on context
                const responses = [
                    '好的，我已记录您的选择。请告诉我您的出行需求，比如出发地、目的地和时间？',
                    '明白了。为了为您提供最佳的差旅方案，请提供更多详细信息。',
                    '收到！我会根据您的选择为您规划最合适的行程。还有什么特殊要求吗？',
                    '谢谢您的信息。我正在为您查找最优的差旅选项...'
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage('assistant', randomResponse);
            }, 2000);
        }

        // Voice button functionality
        document.querySelector('.voice-btn').addEventListener('click', function() {
            this.style.color = this.style.color === 'red' ? '#4285f4' : 'red';
            console.log('Voice input toggled');

            // Simulate voice recognition
            if (this.style.color === 'red') {
                messageInput.placeholder = '正在听取语音输入...';
                setTimeout(() => {
                    this.style.color = '#4285f4';
                    messageInput.placeholder = '输入您的需求，如"帮我预订经济实惠的"';
                }, 3000);
            }
        });

        // Back button functionality
        document.querySelector('.back-btn').addEventListener('click', function() {
            if (confirm('确定要退出聊天吗？')) {
                console.log('Going back');
                // Here you would implement navigation back
            }
        });

        // Header icons functionality
        document.querySelectorAll('.header-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                console.log('Header icon clicked');
                // Here you would implement respective functionality
            });
        });

        // Refresh button functionality
        document.querySelector('.refresh-btn').addEventListener('click', function() {
            if (confirm('确定要重新开始对话吗？')) {
                // Clear chat history
                messagesContainer.innerHTML = '';
                chatHistory = [];
                optionButtons.style.display = 'flex';
                selectedOption = 'self';

                // Reset option selection
                document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
                document.querySelector('[data-option="self"]').classList.add('selected');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Chat interface initialized');
        });
    </script>
</body>
</html>
