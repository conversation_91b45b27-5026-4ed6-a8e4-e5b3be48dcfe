/**
 * 同程智能差旅助手 - 聊天界面交互逻辑
 */

class ChatBox {
    constructor() {
        this.selectedOption = 'self';
        this.chatHistory = [];
        this.isTyping = false;
        
        // DOM elements
        this.messageInput = document.querySelector('.message-input');
        this.sendBtn = document.querySelector('.send-btn');
        this.chatContent = document.querySelector('#chatContent');
        this.messagesContainer = document.querySelector('#messagesContainer');
        this.typingIndicator = document.querySelector('#typingIndicator');
        this.optionButtons = document.querySelector('#optionButtons');
        
        this.init();
    }

    init() {
        this.bindEvents();
        console.log('ChatBox initialized');
    }

    bindEvents() {
        // Option selection
        document.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleOptionSelect(e));
        });

        // Input events
        this.messageInput.addEventListener('input', () => this.handleInputChange());
        this.messageInput.addEventListener('keypress', (e) => this.handleKeyPress(e));
        this.sendBtn.addEventListener('click', () => this.sendMessage());

        // Control buttons
        document.querySelector('.voice-btn').addEventListener('click', () => this.toggleVoice());
        document.querySelector('.back-btn').addEventListener('click', () => this.goBack());
        document.querySelector('.refresh-btn').addEventListener('click', () => this.refreshChat());

        // Header icons
        document.querySelectorAll('.header-icon').forEach(icon => {
            icon.addEventListener('click', () => this.handleHeaderIcon());
        });
    }

    handleOptionSelect(event) {
        const btn = event.currentTarget;
        
        // Update selection
        document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
        btn.classList.add('selected');
        this.selectedOption = btn.dataset.option;
        
        // Send selection as message
        const optionText = this.selectedOption === 'self' ? '本人预订' : '代他人预订';
        this.addMessage('user', `我选择：${optionText}`);
        
        // Hide options and show response
        setTimeout(() => {
            this.optionButtons.style.display = 'none';
            this.simulateAssistantResponse();
        }, 500);
    }

    handleInputChange() {
        this.sendBtn.disabled = this.messageInput.value.trim() === '';
    }

    handleKeyPress(event) {
        if (event.key === 'Enter' && !this.sendBtn.disabled) {
            this.sendMessage();
        }
    }

    sendMessage() {
        const message = this.messageInput.value.trim();
        if (message && !this.isTyping) {
            this.addMessage('user', message);
            this.messageInput.value = '';
            this.sendBtn.disabled = true;
            
            // Simulate assistant response
            setTimeout(() => {
                this.simulateAssistantResponse();
            }, 1000);
        }
    }

    addMessage(sender, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-bubble ${sender}`;
        
        const now = new Date();
        const timeStr = now.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        messageDiv.innerHTML = `
            <div class="message-content">${this.escapeHtml(text)}</div>
        `;
        
        this.messagesContainer.appendChild(messageDiv);
        this.chatHistory.push({ sender, text, time: timeStr });
        
        // Scroll to bottom
        this.scrollToBottom();
    }

    showTyping() {
        this.isTyping = true;
        this.typingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }

    hideTyping() {
        this.isTyping = false;
        this.typingIndicator.style.display = 'none';
    }

    simulateAssistantResponse() {
        this.showTyping();
        
        setTimeout(() => {
            this.hideTyping();
            
            // Generate contextual response
            const response = this.generateResponse();
            this.addMessage('assistant', response);
        }, 1500 + Math.random() * 1000); // Random delay for realism
    }

    generateResponse() {
        const responses = {
            greeting: [
                '好的，我已记录您的选择。请告诉我您的出行需求，比如出发地、目的地和时间？',
                '明白了。为了为您提供最佳的差旅方案，请提供更多详细信息。',
                '收到！我会根据您的选择为您规划最合适的行程。'
            ],
            general: [
                '我正在为您查找最优的差旅选项，请稍等...',
                '根据您的需求，我推荐以下几个方案：',
                '让我为您查询相关信息，请稍候。',
                '好的，我来帮您处理这个需求。'
            ],
            travel: [
                '请提供您的出发城市和目的地城市。',
                '您希望什么时候出发？返程时间是什么时候？',
                '您对住宿有什么特殊要求吗？',
                '需要预订机票还是火车票？'
            ]
        };

        // Simple context-based response selection
        const lastUserMessage = this.getLastUserMessage();
        let category = 'general';
        
        if (this.chatHistory.length <= 2) {
            category = 'greeting';
        } else if (this.containsTravelKeywords(lastUserMessage)) {
            category = 'travel';
        }
        
        const categoryResponses = responses[category];
        return categoryResponses[Math.floor(Math.random() * categoryResponses.length)];
    }

    getLastUserMessage() {
        for (let i = this.chatHistory.length - 1; i >= 0; i--) {
            if (this.chatHistory[i].sender === 'user') {
                return this.chatHistory[i].text;
            }
        }
        return '';
    }

    containsTravelKeywords(text) {
        const keywords = ['出发', '目的地', '机票', '火车', '酒店', '住宿', '时间', '预订'];
        return keywords.some(keyword => text.includes(keyword));
    }

    toggleVoice() {
        const voiceBtn = document.querySelector('.voice-btn');
        const isActive = voiceBtn.style.color === 'red';
        
        voiceBtn.style.color = isActive ? '#4285f4' : 'red';
        
        if (!isActive) {
            this.messageInput.placeholder = '正在听取语音输入...';
            // Simulate voice recognition
            setTimeout(() => {
                voiceBtn.style.color = '#4285f4';
                this.messageInput.placeholder = '输入您的需求，如"帮我预订经济实惠的"';
            }, 3000);
        }
    }

    goBack() {
        if (confirm('确定要退出聊天吗？')) {
            console.log('Going back');
            // Here you would implement navigation back
            // window.history.back() or redirect to previous page
        }
    }

    refreshChat() {
        if (confirm('确定要重新开始对话吗？')) {
            // Clear chat history
            this.messagesContainer.innerHTML = '';
            this.chatHistory = [];
            this.optionButtons.style.display = 'flex';
            this.selectedOption = 'self';
            this.isTyping = false;
            
            // Reset option selection
            document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
            document.querySelector('[data-option="self"]').classList.add('selected');
            
            // Reset input
            this.messageInput.value = '';
            this.sendBtn.disabled = true;
        }
    }

    handleHeaderIcon() {
        console.log('Header icon clicked');
        // Implement menu or settings functionality
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatContent.scrollTop = this.chatContent.scrollHeight;
        }, 100);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API methods
    addCustomMessage(sender, text) {
        this.addMessage(sender, text);
    }

    getChatHistory() {
        return [...this.chatHistory];
    }

    clearChat() {
        this.refreshChat();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.chatBox = new ChatBox();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatBox;
}
