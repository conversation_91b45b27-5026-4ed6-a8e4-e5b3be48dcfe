<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能差旅助手</title>
    <link rel="stylesheet" href="chatbox.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <button class="back-btn" title="返回">←</button>
                <span class="header-title">智能差旅助手</span>
            </div>
            <div class="header-right">
                <button class="header-icon" title="更多">⋯</button>
                <button class="header-icon" title="设置">⚙</button>
            </div>
        </div>

        <!-- Chat Content -->
        <div class="chat-content" id="chatContent">
            <!-- Welcome Card -->
            <div class="assistant-card">
                <div class="assistant-header">
                    <div class="assistant-avatar">AI</div>
                    <div class="assistant-info">
                        <h3>智能助手</h3>
                        <p>为您规划出行</p>
                    </div>
                </div>

                <div class="welcome-message">
                    您好！我是您的智能差旅助手。请选择预订类型以开始规划您的行程。
                </div>
                
                <!-- Option Buttons -->
                <div class="option-buttons" id="optionButtons">
                    <div class="option-btn selected" data-option="self">
                        <div class="check-mark">✓</div>
                        <div class="icon">●</div>
                        <div class="title">个人出行</div>
                        <div class="desc">为自己预订差旅行程</div>
                    </div>

                    <div class="option-btn" data-option="others">
                        <div class="check-mark">✓</div>
                        <div class="icon">●●</div>
                        <div class="title">团队出行</div>
                        <div class="desc">为同事或团队预订行程</div>
                    </div>
                </div>
            </div>

            <!-- Messages Container -->
            <div id="messagesContainer"></div>
            
            <!-- Typing Indicator -->
            <div class="message-bubble assistant">
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <button class="refresh-btn" title="重新开始">↻</button>
            <button class="voice-btn" title="语音输入">◉</button>
            <div class="input-wrapper">
                <input type="text" class="message-input" placeholder="请输入您的出行需求..." autocomplete="off">
            </div>
            <button class="send-btn" title="发送" disabled>→</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="chatbox.js"></script>
</body>
</html>
