<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行程提交成功 - 智能差旅助手</title>
    <link rel="stylesheet" href="success.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✓</text></svg>">
</head>
<body>
    <div class="success-container">
        <!-- Success Icon -->
        <div class="success-icon">✓</div>
        
        <!-- Success Message -->
        <h1 class="success-title">您的行程提交成功！</h1>
        <p class="success-subtitle">请注意查收短信并进行后续处理。</p>
        
        <!-- Order Information -->
        <div class="order-info">
            <div class="info-row">
                <span class="info-label">行程单号：</span>
                <span class="info-value clickable-order-id" onclick="copyOrderId()" title="点击复制">TC000299992323323</span>
            </div>
            <div class="info-row">
                <span class="info-label">创建时间：</span>
                <span class="info-value" id="createTime">2025-08-05 12:23:23</span>
            </div>
            <div class="info-row">
                <span class="info-label">行程单状态：</span>
                <span class="info-value">
                    <span class="status-pending" id="orderStatus">待支付</span>
                </span>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <button class="detail-btn" onclick="viewDetails()">查看详情</button>
        <button class="back-btn" onclick="goBack()">返回首页</button>
        
        <!-- Tips -->
        <div class="tips">
            <span class="tips-icon">💡</span>
            我们已向您的手机发送确认短信，请注意查收。如需修改行程，请在支付前联系客服。
        </div>
    </div>

    <script src="success.js"></script>
</body>
</html>
