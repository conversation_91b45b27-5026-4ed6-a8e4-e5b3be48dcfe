/* 行程提交成功页面样式 - 现代简约风格 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    color: #2c3e50;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* 主容器 */
.success-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 40px 32px;
    max-width: 400px;
    width: 100%;
    text-align: center;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    animation: fadeInUp 0.5s ease-out;
}

/* 成功图标 */
.success-icon {
    width: 60px;
    height: 60px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 24px;
    font-weight: bold;
    animation: checkmark 0.6s ease-out 0.2s both;
}

/* 标题和副标题 */
.success-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.success-subtitle {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 32px;
    line-height: 1.5;
}

/* 订单信息卡片 */
.order-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 32px;
    text-align: left;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.info-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.info-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.info-value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
    text-align: right;
    max-width: 60%;
    word-break: break-all;
}

/* 状态标签 */
.status-pending {
    color: #fd7e14;
    background: #fff3cd;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.status-paid {
    color: #28a745;
    background: #d4edda;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.status-completed {
    color: #007bff;
    background: #cce7ff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.status-cancelled {
    color: #dc3545;
    background: #f8d7da;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

/* 按钮样式 */
.detail-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px 32px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    margin-bottom: 16px;
}

.detail-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.detail-btn:active {
    transform: translateY(0);
}

.back-btn {
    background: none;
    color: #6c757d;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 32px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.back-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.back-btn:active {
    background: #e9ecef;
}

/* 提示信息 */
.tips {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 6px;
    padding: 12px;
    margin-top: 24px;
    font-size: 13px;
    color: #004085;
    line-height: 1.4;
    text-align: left;
}

.tips-icon {
    display: inline-block;
    margin-right: 6px;
    font-weight: bold;
}

/* 可点击的订单号 */
.clickable-order-id {
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: dashed;
}

.clickable-order-id:hover {
    color: #007bff;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes checkmark {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInOut {
    0% { 
        opacity: 0; 
        transform: translateX(-50%) translateY(-10px); 
    }
    20%, 80% { 
        opacity: 1; 
        transform: translateX(-50%) translateY(0); 
    }
    100% { 
        opacity: 0; 
        transform: translateX(-50%) translateY(-10px); 
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    body {
        padding: 16px;
    }
    
    .success-container {
        padding: 32px 24px;
        margin: 0;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .info-value {
        text-align: left;
        max-width: 100%;
    }
    
    .success-title {
        font-size: 16px;
    }
    
    .success-subtitle {
        font-size: 13px;
    }
}

@media (max-width: 360px) {
    .success-container {
        padding: 24px 20px;
    }
    
    .order-info {
        padding: 20px;
    }
    
    .detail-btn {
        padding: 12px 24px;
        font-size: 15px;
    }
    
    .back-btn {
        padding: 10px 24px;
        font-size: 13px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: #1a1a1a;
        color: #e9ecef;
    }
    
    .success-container {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .order-info {
        background: #1a202c;
    }
    
    .info-row {
        border-color: #4a5568;
    }
    
    .info-label {
        color: #a0aec0;
    }
    
    .info-value {
        color: #e2e8f0;
    }
    
    .back-btn {
        border-color: #4a5568;
        color: #a0aec0;
    }
    
    .back-btn:hover {
        background: #2d3748;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .tips {
        background: #2a4a6b;
        border-color: #3182ce;
        color: #bee3f8;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
        padding: 0;
    }
    
    .success-container {
        box-shadow: none;
        border: 1px solid #000;
        max-width: none;
        width: 100%;
    }
    
    .detail-btn,
    .back-btn {
        display: none;
    }
    
    .tips {
        border: 1px solid #000;
        background: #f5f5f5;
    }
}
