<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行程提交成功 - 智能差旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #2c3e50;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .success-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 40px 32px;
            max-width: 400px;
            width: 100%;
            text-align: center;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }

        .success-icon {
            width: 60px;
            height: 60px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .success-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .success-subtitle {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .order-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 32px;
            text-align: left;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e9ecef;
        }

        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .info-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-all;
        }

        .status-pending {
            color: #fd7e14;
            background: #fff3cd;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .detail-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 32px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
            margin-bottom: 16px;
        }

        .detail-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .back-btn {
            background: none;
            color: #6c757d;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 32px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .back-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
            color: #495057;
        }

        .tips {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 12px;
            margin-top: 24px;
            font-size: 13px;
            color: #004085;
            line-height: 1.4;
        }

        .tips-icon {
            display: inline-block;
            margin-right: 6px;
            font-weight: bold;
        }

        @media (max-width: 480px) {
            .success-container {
                padding: 32px 24px;
                margin: 0 16px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .info-value {
                text-align: left;
                max-width: 100%;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-container {
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes checkmark {
            0% {
                transform: scale(0);
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
            }
        }

        .success-icon {
            animation: checkmark 0.6s ease-out 0.2s both;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <!-- Success Icon -->
        <div class="success-icon">✓</div>
        
        <!-- Success Message -->
        <h1 class="success-title">您的行程提交成功！</h1>
        <p class="success-subtitle">请注意查收短信并进行后续处理。</p>
        
        <!-- Order Information -->
        <div class="order-info">
            <div class="info-row">
                <span class="info-label">行程单号：</span>
                <span class="info-value">TC000299992323323</span>
            </div>
            <div class="info-row">
                <span class="info-label">创建时间：</span>
                <span class="info-value">2025-08-05 12:23:23</span>
            </div>
            <div class="info-row">
                <span class="info-label">行程单状态：</span>
                <span class="info-value">
                    <span class="status-pending">待支付</span>
                </span>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <button class="detail-btn" onclick="viewDetails()">查看详情</button>
        <button class="back-btn" onclick="goBack()">返回首页</button>
        
        <!-- Tips -->
        <div class="tips">
            <span class="tips-icon">💡</span>
            我们已向您的手机发送确认短信，请注意查收。如需修改行程，请在支付前联系客服。
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加页面统计或其他初始化逻辑
            console.log('Success page loaded');
            
            // 模拟自动刷新状态（可选）
            // setTimeout(checkOrderStatus, 30000); // 30秒后检查状态
        });

        // 查看详情
        function viewDetails() {
            // 这里可以跳转到详情页面或打开详情弹窗
            console.log('View details clicked');
            
            // 示例：跳转到详情页面
            // window.location.href = 'order-details.html?id=TC000299992323323';
            
            // 示例：打开新窗口
            // window.open('order-details.html?id=TC000299992323323', '_blank');
            
            // 临时提示
            alert('详情页面开发中，敬请期待！');
        }

        // 返回首页
        function goBack() {
            console.log('Go back clicked');
            
            // 可以返回聊天界面或主页
            if (confirm('确定要返回首页吗？')) {
                // window.location.href = 'index.html';
                // 或者返回上一页
                window.history.back();
            }
        }

        // 检查订单状态（可选功能）
        function checkOrderStatus() {
            // 这里可以调用API检查订单状态
            console.log('Checking order status...');
            
            // 示例API调用
            /*
            fetch('/api/order/status?id=TC000299992323323')
                .then(response => response.json())
                .then(data => {
                    if (data.status !== '待支付') {
                        updateOrderStatus(data.status);
                    }
                })
                .catch(error => {
                    console.error('Error checking order status:', error);
                });
            */
        }

        // 更新订单状态显示
        function updateOrderStatus(newStatus) {
            const statusElement = document.querySelector('.status-pending');
            if (statusElement) {
                statusElement.textContent = newStatus;
                statusElement.className = getStatusClass(newStatus);
            }
        }

        // 根据状态获取样式类
        function getStatusClass(status) {
            switch (status) {
                case '待支付':
                    return 'status-pending';
                case '已支付':
                    return 'status-paid';
                case '已完成':
                    return 'status-completed';
                case '已取消':
                    return 'status-cancelled';
                default:
                    return 'status-pending';
            }
        }

        // 复制订单号功能
        function copyOrderId() {
            const orderId = 'TC000299992323323';
            navigator.clipboard.writeText(orderId).then(function() {
                // 显示复制成功提示
                showToast('订单号已复制到剪贴板');
            }).catch(function(err) {
                console.error('复制失败:', err);
            });
        }

        // 显示提示信息
        function showToast(message) {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #28a745;
                color: white;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeInOut 2s ease-in-out;
            `;
            
            document.body.appendChild(toast);
            
            // 2秒后移除
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 2000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
                20%, 80% { opacity: 1; transform: translateX(-50%) translateY(0); }
                100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
