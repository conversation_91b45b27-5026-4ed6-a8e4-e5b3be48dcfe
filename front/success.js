/**
 * 行程提交成功页面交互逻辑
 */

class SuccessPage {
    constructor() {
        this.orderId = 'TC000299992323323';
        this.orderStatus = '待支付';
        this.createTime = '2025-08-05 12:23:23';
        
        this.init();
    }

    init() {
        console.log('Success page initialized');
        
        // 从URL参数获取订单信息（如果有）
        this.loadOrderFromURL();
        
        // 设置自动状态检查
        this.setupStatusCheck();
        
        // 绑定键盘事件
        this.bindKeyboardEvents();
    }

    // 从URL参数加载订单信息
    loadOrderFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        
        if (urlParams.has('orderId')) {
            this.orderId = urlParams.get('orderId');
            document.querySelector('.clickable-order-id').textContent = this.orderId;
        }
        
        if (urlParams.has('status')) {
            this.orderStatus = urlParams.get('status');
            this.updateOrderStatus(this.orderStatus);
        }
        
        if (urlParams.has('createTime')) {
            this.createTime = urlParams.get('createTime');
            document.getElementById('createTime').textContent = this.createTime;
        }
    }

    // 设置状态检查
    setupStatusCheck() {
        // 每30秒检查一次订单状态
        setInterval(() => {
            this.checkOrderStatus();
        }, 30000);
    }

    // 绑定键盘事件
    bindKeyboardEvents() {
        document.addEventListener('keydown', (event) => {
            switch (event.key) {
                case 'Enter':
                    this.viewDetails();
                    break;
                case 'Escape':
                    this.goBack();
                    break;
                case 'c':
                case 'C':
                    if (event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        this.copyOrderId();
                    }
                    break;
            }
        });
    }

    // 查看详情
    viewDetails() {
        console.log('View details clicked for order:', this.orderId);
        
        // 显示加载状态
        const btn = document.querySelector('.detail-btn');
        const originalText = btn.textContent;
        btn.textContent = '加载中...';
        btn.classList.add('loading');
        
        // 模拟API调用
        setTimeout(() => {
            btn.textContent = originalText;
            btn.classList.remove('loading');
            
            // 实际项目中这里应该跳转到详情页面
            // window.location.href = `order-details.html?id=${this.orderId}`;
            
            // 临时提示
            this.showToast('详情页面开发中，敬请期待！');
        }, 1000);
    }

    // 返回首页
    goBack() {
        console.log('Go back clicked');
        
        if (confirm('确定要返回首页吗？')) {
            // 可以返回聊天界面或主页
            // window.location.href = 'index.html';
            
            // 或者返回上一页
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = 'index.html';
            }
        }
    }

    // 复制订单号
    copyOrderId() {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(this.orderId).then(() => {
                this.showToast('订单号已复制到剪贴板');
                
                // 添加视觉反馈
                const element = document.querySelector('.clickable-order-id');
                element.style.color = '#28a745';
                setTimeout(() => {
                    element.style.color = '';
                }, 1000);
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyOrderId();
            });
        } else {
            this.fallbackCopyOrderId();
        }
    }

    // 备用复制方法
    fallbackCopyOrderId() {
        const textArea = document.createElement('textarea');
        textArea.value = this.orderId;
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showToast('订单号已复制到剪贴板');
        } catch (err) {
            console.error('复制失败:', err);
            this.showToast('复制失败，请手动复制订单号');
        }
        
        document.body.removeChild(textArea);
    }

    // 检查订单状态
    async checkOrderStatus() {
        try {
            console.log('Checking order status for:', this.orderId);
            
            // 实际项目中这里应该调用真实的API
            /*
            const response = await fetch(`/api/order/status?id=${this.orderId}`);
            const data = await response.json();
            
            if (data.success && data.status !== this.orderStatus) {
                this.updateOrderStatus(data.status);
                this.showToast(`订单状态已更新：${data.status}`);
            }
            */
            
            // 模拟状态变化（仅用于演示）
            if (Math.random() < 0.1) { // 10%概率模拟状态变化
                const statuses = ['待支付', '已支付', '已完成'];
                const currentIndex = statuses.indexOf(this.orderStatus);
                if (currentIndex < statuses.length - 1) {
                    const newStatus = statuses[currentIndex + 1];
                    this.updateOrderStatus(newStatus);
                    this.showToast(`订单状态已更新：${newStatus}`);
                }
            }
        } catch (error) {
            console.error('检查订单状态失败:', error);
        }
    }

    // 更新订单状态显示
    updateOrderStatus(newStatus) {
        this.orderStatus = newStatus;
        const statusElement = document.getElementById('orderStatus');
        
        if (statusElement) {
            statusElement.textContent = newStatus;
            statusElement.className = this.getStatusClass(newStatus);
            
            // 添加更新动画
            statusElement.style.transform = 'scale(1.1)';
            setTimeout(() => {
                statusElement.style.transform = 'scale(1)';
            }, 200);
        }
    }

    // 根据状态获取样式类
    getStatusClass(status) {
        const statusMap = {
            '待支付': 'status-pending',
            '已支付': 'status-paid',
            '已完成': 'status-completed',
            '已取消': 'status-cancelled'
        };
        
        return statusMap[status] || 'status-pending';
    }

    // 显示提示信息
    showToast(message, type = 'success') {
        // 移除已存在的toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }
        
        // 创建新的toast
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        
        // 设置样式
        const bgColor = type === 'success' ? '#28a745' : 
                       type === 'error' ? '#dc3545' : 
                       type === 'warning' ? '#ffc107' : '#007bff';
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${bgColor};
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
            animation: fadeInOut 3s ease-in-out;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        `;
        
        document.body.appendChild(toast);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }

    // 获取订单信息
    getOrderInfo() {
        return {
            orderId: this.orderId,
            status: this.orderStatus,
            createTime: this.createTime
        };
    }

    // 刷新页面数据
    refresh() {
        this.checkOrderStatus();
        this.showToast('页面已刷新');
    }
}

// 全局函数（保持向后兼容）
function viewDetails() {
    if (window.successPage) {
        window.successPage.viewDetails();
    }
}

function goBack() {
    if (window.successPage) {
        window.successPage.goBack();
    }
}

function copyOrderId() {
    if (window.successPage) {
        window.successPage.copyOrderId();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.successPage = new SuccessPage();
    
    // 添加CSS动画样式
    if (!document.querySelector('#dynamic-styles')) {
        const style = document.createElement('style');
        style.id = 'dynamic-styles';
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
                15%, 85% { opacity: 1; transform: translateX(-50%) translateY(0); }
                100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
            }
            
            .clickable-order-id {
                transition: color 0.3s ease;
            }
            
            #orderStatus {
                transition: transform 0.2s ease;
            }
            
            .loading {
                opacity: 0.7;
                pointer-events: none;
            }
        `;
        document.head.appendChild(style);
    }
});

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SuccessPage;
}
